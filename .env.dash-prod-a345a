GOOGLE_PLACES_KEY=AIzaSyDwKGT_yvHHXn3H_v5T6RTbjpBpGz17HKQ
ACTIVATION_URL_PREFIX=https://dash-prod-a345a.web.app/activation/
RECEIPT_URL=https://app.d-ash.com/receipt
SOEPAY_API_KEY=aceab05394db5a39115d38fc526619d4b02277b6d8cdee5fb6f0fdc1870fe185
SOEPAY_IDENTITY=e8c6797f506ae894d9130e7490516deeef99f8a761b667c31ca41dcced85cb47
SOEPAY_APP_ID=c8ade19f0cdfb229fd67726764a8bf401f4038de
SOEPAY_URL=https://api.soepay.com/spgs
TWILIO_ACCOUNT_SID=**********************************
TWILIO_TOKEN=08e297923719009a9338e852c7757901
TWILIO_PHONE_NUMBER=+***********
WEB_API_KEY=AIzaSyAo4WNNhUZZHHuOjHyDRS4UoBfnp7Lgx6E
PGSQL_PORT=5432
PGSQL_SOCKET_MASTER=/cloudsql/dash-prod-a345a:asia-east2:dash
PGSQL_SOCKET_SLAVE=/cloudsql/dash-prod-a345a:asia-east2:dash
PGSQL_SOCKET_SLAVE=/cloudsql/dash-prod-a345a:asia-east2:dash-replica-read0
PGSQL_HOST=
PGSQL_USERNAME=dash
PGSQL_PASSWORD=ejfEiszgZG1yUX%
PGSQL_DATABASE=dash
SWAGGER_BASE_PATH=
INTEGRATION_TEST_ENDPOINT=https://api.dash-hk.com/
GLOBAL_PAYMENT_ENV=api
GLOBAL_PAYMENT_KEY_FILENAME=rest
GLOBAL_PAYMENT_MERCHANT_ID=gphk088028225200
GLOBAL_PAYMENT_MERCHANT_KEY_ID=c45a2a64-1bc4-4299-9b68-0d4dac355d2c
GLOBAL_PAYMENT_MERCHANT_KEY_SECRET=0t9hsB2QMdTzviWLSq3/EZjYjpb+jfBan8+jMG+PZcA=
QR_CODE_URL_DASH=l.d-ash.com
GLOBAL_PAYMENT_CLIENT_REFERENCE_CODE=DASH
GLOBAL_PAYMENT_BACKEND_URL=https://api.dash-hk.com
SENDGRID_API_KEY=*********************************************************************
WEB_ADMIN_URL=https://web-admin-72527.web.app
GCP_PROJECT_ID=269039236522
HAILING_BASE_URL=https://api.dash-hk.com/hailing/
KRAKEN_API_URL=https://backend-kraken-usx5pcjzxa-df.a.run.app
SENTRY_AUTH_TOKEN="***************************************************************************************************************************************************************************************************************"
SENTRY_DSN=https://<EMAIL>/4508199595278416
KRAKEN_API_KEY=57c5a0f1-d9e2-4ebb-ad3d-5f8565bd110c
WEB_SECURE_URL=https://web-secure.dash-hk.com

SYNCAB_API_URL=https://jmvpp07v3d.execute-api.ap-northeast-2.amazonaws.com
SYNCAB_MOCK_API=https://qa-api-tools-210975360305.asia-east2.run.app
MOCK_SYNCAB_API=false

BASE_URL=https://api.dash-hk.com
CLOUD_TASKS_LOCATION=asia-east2
CLOUD_TASKS_API_KEY=dash8LvPuqmzMgBghSgvYxq4T4d6kg6a3NZh7RE1LjNLlp0ov42st54dv7POvZWwkx9tKAeqgWnZqOHQMkAHnEhrzmkcEZqKDmqADGbIKTUMxnQjLzoTQynSX3NP9wBaOpWAuvknRN8fHJjtYwr0scvd6El2YOk1LlsmN3tZl8JW6ADdqhHcZOMI7VXet5k8h1tJEUKTmsbUuhS7Mc2SAaZ4TnclMoSYmlKOb824wUMwH9ULiSOIf6XZ5DVuMGnt

TEAMS_WEBHOOK_URL=https://netorgft8809665.webhook.office.com/webhookb2/62d277bc-8fc0-4fda-87f0-52e1cac80376@42553b9c-b625-4c8d-9139-fa1af19f40fb/IncomingWebhook/d2f56a9dec1c41018a279ae406030652/ca8aacdc-6eab-44c4-a2fe-ebbc89691136/V2JCvxDfOQVYXozoYiZS9CuXSJ-yOLQuxvYeX8Jcc1zzU1

REDIS_PUBSUB_URL=redis://default@10.82.225.12:6379
REDIS_PUBSUB_DB=2 # ideally something unused
