pubsub2redis:
  - name: pubsub2redisTripEndEventDash3064
    pubsubTopic: "trip-end-event"
    redisTopic: "trip-end-event-dash-3064"
    radius: "100"
    filter: {} # JsonLogic filter
  - name: pubsub2redisHailingStatusChangedDash3064
    pubsubTopic: "hailing-status-changed"
    redisTopic: "hailing-status-changed-dash-3064"
    radius: "100"
    filter: '{"and": [{"!=": [{"var": "status"}, "CANCELLED"]}, {"!=": [{"var": "status"}, "TIMED_OUT"]}]}' # JsonLogic filter
  - name: pubsub2redisHailingTxCreatedDash3064
    pubsubTopic: "hailing-tx-created"
    redisTopic: "hailing-tx-created-dash-3064"
    radius: "100"
    filter: {} # JsonLogic filter
  # - name: pubsub2redisTripProcessing
  #   pubsubTopic: "trip-processing"
  #   radius: "100"
  #   filter: {} # custom, to be defined
  # - name: pubsub2redisPickupReminder
  #   pubsubTopic: "pickup-reminder"
  #   radius: "100"
  #   filter: {} # custom, to be defined
