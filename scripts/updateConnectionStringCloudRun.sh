#!/bin/bash
# 
# QA: dash-qa-cdfcd:asia-east2:dash,dash-qa-cdfcd:asia-east2:dash-replica-read0
# command: ./scripts/updateConnectionStringCloudRun.sh dash-dev2-edcb3 dash-dev2-edcb3:asia-east2:dash,dash-dev2-edcb3:asia-east2:dash-replica-read0
# remember to replace dash-dev2-edcb3 with project id
updateFunction() { 
  echo "------------------------------------------------------------------------------------"
  echo "Updating $1..."
  gcloud run services update $1 \
    --region asia-east2 \
    --project $2 \
    --set-cloudsql-instances $3
  echo "Done $1"
  echo "------------------------------------------------------------------------------------"
}

### Explicitly exclude kraken functions
### Explicitly set the project id
FUNCTIONS=$(gcloud run services list --project $1| grep -v kraken  | sed 's/|/ /' | awk '{print $2}')

for FUNCTION in $FUNCTIONS
do
  if [[ "$FUNCTION" = "REGION" ]]; then
    continue
  fi

  {
    updateFunction $FUNCTION $1 $2
  } &
done
