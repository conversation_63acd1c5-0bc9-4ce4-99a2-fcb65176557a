import { CloudEvent, ParamsOf } from "firebase-functions/v2";
import {
  CrashlyticsEvent,
  NewFatalIssuePayload,
  onNewFatalIssuePublished,
} from "firebase-functions/v2/alerts/crashlytics";
import {
  Change,
  DocumentSnapshot,
  FirestoreEvent,
  QueryDocumentSnapshot,
  onDocumentCreated,
  onDocumentUpdated,
  onDocumentWritten,
} from "firebase-functions/v2/firestore";
import { onRequest } from "firebase-functions/v2/https";
import { AuthBlockingEvent, beforeUserSignedIn } from "firebase-functions/v2/identity";
import { MessagePublishedData, onMessagePublished } from "firebase-functions/v2/pubsub";
import { ScheduledEvent, onSchedule } from "firebase-functions/v2/scheduler";
import { StorageEvent, onObjectFinalized } from "firebase-functions/v2/storage";

import { CampaignService } from "@nest/modules/campaign/campaign.service";
import { PaymentService } from "@nest/modules/payment/payment.service";
import { PublishMessageForCampaignTriggerProcessingParams } from "@nest/modules/pubsub/dto/publishMessageForCampaignTriggerProcessingParams";
import { PublishMessageForHailingStatusChangedParams } from "@nest/modules/pubsub/dto/publishMessageForHailingStatusChangedParams.dto";
import {
  PublishMessageForHeartbeatProcessing,
  publishMessageForHeartbeatProcessingSchema,
} from "@nest/modules/pubsub/dto/publishMessageForHeartbeatProcessing.dto";
import { PublishMessageForPickupReminderParams } from "@nest/modules/pubsub/dto/publishMessageForPickupReminderParams.dto";
import { PublishMessageForReportJobProcessingParams } from "@nest/modules/pubsub/dto/publishMessageForReportJobProcessingParams.dto";
import {
  PublishMessageForWebhookProcessing,
  publishMessageForWebhookProcessingSchema,
} from "@nest/modules/pubsub/dto/PublishMessageForWebhookProcessing.dto";
import { ReportJobController } from "@nest/modules/reportJob/reportJob.controller";
import { TeamOrderNotificationController } from "@nest/modules/teamOrderNotification/teamOrderNotification.controller";
import { WebhookController } from "@nest/modules/webhook/webhook.controller";

import { AuthController } from "../nestJs/modules/auth/auth.controller";
import { BankNames } from "../nestJs/modules/bank/dto/bankName.dto";
import { FcmService } from "../nestJs/modules/fcm/fcm.service";
import { IdentityController } from "../nestJs/modules/identity/identity.controller";
import { MerchantDriverController } from "../nestJs/modules/merchant/merchantDriver/merchantDriver.controller";
import { MessageController } from "../nestJs/modules/message/message.controller";
import { MessageTeamsController } from "../nestJs/modules/messageTeams/messageTeams.controller";
import { MeterController } from "../nestJs/modules/meter/meter.controller";
import { nestServiceInstance } from "../nestJs/modules/nest/nest.service";
import { PaymentController } from "../nestJs/modules/payment/payment.controller";
import { PublishMessageForCaptureProcessingParams } from "../nestJs/modules/pubsub/dto/publishMessageForCaptureProcessingParams.dto";
import { PublishMessageForCopyTripToDriverProcessingParams } from "../nestJs/modules/pubsub/dto/publishMessageForDriverTripProcessing.dto";
import { PublishMessageForHailingTxCreatedParams } from "../nestJs/modules/pubsub/dto/publishMessageForHailingTxCreatedParams.dto";
import { PublishMessageForIdentityProcessingParams } from "../nestJs/modules/pubsub/dto/publishMessageForIdentityProcessingParams.dto";
import { PublishMessageForMessageProcessingParams } from "../nestJs/modules/pubsub/dto/publishMessageForMessageProcessing.dto";
import { PublishMessageForPushNotificationProcessingParams } from "../nestJs/modules/pubsub/dto/publishMessageForPushNotificationProcessingParams.dto";
import { PublishMessageForSaleProcessingParams } from "../nestJs/modules/pubsub/dto/PublishMessageForSaleProcessingParams.dto";
import { PublishMessageForTripProcessingParams } from "../nestJs/modules/pubsub/dto/publishMessageForTripProcessing.dto";
import {
  PublishMessageForTxEventSystemParams,
  publishMessageForTxEventSystemParamsSchema,
} from "../nestJs/modules/pubsub/dto/publishMessageForTxEventSystemParams.dto";
import { PublishMessageForVoidProcessingParams } from "../nestJs/modules/pubsub/dto/publishMessageForVoidProcessingParams.dto";
import { PublishMessageForDirectSaleProcessingParams } from "../nestJs/modules/pubsub/dto/publishMessageToProcessSale.dto";
import { PubSubService } from "../nestJs/modules/pubsub/pubsub.service";
import { Buckets } from "../nestJs/modules/storage/storage.service";
import { SystemTransactionService } from "../nestJs/modules/system/modules/systemTransaction/systemTransaction.service";
import { TransactionController } from "../nestJs/modules/transaction/transaction.controller";
import { TransactionService } from "../nestJs/modules/transaction/transaction.service";
import { TripController } from "../nestJs/modules/transaction/transactionFactory/modules/trip/trip.controller";
import { UserController } from "../nestJs/modules/user/user.controller";
import { DeadLetterQueueMessage } from "../nestJs/modules/utils/CustomFunction/CustomFunction";
import loggerUtils from "../nestJs/modules/utils/utils/logger.utils";
import { ValidationService } from "../nestJs/modules/validation/validation.service";

import { defaultNestHttpsOptions, defaultNestOptions } from "./_defaultOptions";
import { PublishMessageForTripEndEventParams } from "@nest/modules/pubsub/dto/publishMessageForTripEndEvent.dto";

export const api = onRequest(defaultNestHttpsOptions, async (request, response) => {
  await nestServiceInstance.createNestServer();
  nestServiceInstance.server(request, response);
});

export const payoutFileProcessing = onObjectFinalized(
  { ...defaultNestOptions, bucket: `${process.env.GCLOUD_PROJECT}-${Buckets.PAYOUT_RESPONSE}`, minInstances: 1 },
  nestServiceInstance.listenerWrapper<StorageEvent>(async (app, event) => {
    const { name } = event.data;
    const bankName = name.split("/")[0] as BankNames;

    if (!Object.values(BankNames).includes(bankName)) {
      loggerUtils.error(
        `nest trigger: file added to bucket ${process.env.GCLOUD_PROJECT}-${Buckets.PAYOUT_RESPONSE} but doesn't match any BankName`,
        {
          data: { fileName: event.data.name, bankName },
        },
        new Error("File doesn't match any BankName"),
      );
      return;
    }

    loggerUtils.info(`nest trigger: file added to bucket ${process.env.GCLOUD_PROJECT}-${Buckets.PAYOUT_RESPONSE}`, {
      data: { fileName: event.data.name },
    });
    return app.get(TransactionService).processPayoutFileFromBank(bankName, event.data.name);
  }),
);

/**
 * Function to process a meter trip change
 */
export const meterTripChangedNest = onDocumentUpdated(
  { ...defaultNestOptions, document: "meters/{meterId}/trips/{tripId}", memory: "1GiB" },
  nestServiceInstance.listenerWrapper<
    FirestoreEvent<Change<DocumentSnapshot> | undefined, ParamsOf<"meters/{meterId}/trips/{tripId}">>
  >(async (app, event) => {
    return app.get(TripController).meterTripChange(event);
  }),
);

/**
 * Function to process a meter trip create
 */
export const meterTripCreatedNest = onDocumentCreated(
  { ...defaultNestOptions, document: "meters/{meterId}/trips/{tripId}" },
  nestServiceInstance.listenerWrapper<
    FirestoreEvent<QueryDocumentSnapshot | undefined, ParamsOf<"meters/{meterId}/trips/{tripId}">>
  >(async (app, event) => {
    return app.get(TripController).meterTripCreate(event);
  }),
);

/**
 * Function to process meter creation in firestore
 */
export const meterCreatedNest = onDocumentCreated(
  { ...defaultNestOptions, document: "meters/{meterId}" },
  nestServiceInstance.listenerWrapper<FirestoreEvent<QueryDocumentSnapshot | undefined, ParamsOf<"meters/{meterId}">>>(
    async (app, event) => {
      loggerUtils.info("meterCreatedNest ", event);
      return app.get(MeterController).meterCreateEvent(event);
    },
  ),
);

/**
 * Function to process a transaction
 */
export const txProcessing = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.TRIP_PROCESSING, memory: "1GiB" },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForTripProcessingParams>>>(
    (app, event) => {
      return app.get(TransactionController).processTransaction(event);
    },
  ),
);

/**
 * Function to process a message
 */
export const messageProcessing = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.MESSAGE_PROCESSING, minInstances: 1 },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForMessageProcessingParams>>>(
    (app, event) => {
      return app.get(MessageController).processMessage(event);
    },
  ),
);

export const driverUpdated = onDocumentWritten(
  { ...defaultNestOptions, document: "drivers/{driverId}" },
  nestServiceInstance.listenerWrapper<
    FirestoreEvent<Change<DocumentSnapshot> | undefined, ParamsOf<"drivers/{driverId}">>
  >(async (app, event) => {
    return app.get(MerchantDriverController).updateMerchant(event);
  }),
);

/**
 * Function to process capture
 */
export const captureProcessing = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.CAPTURE_PROCESSING, minInstances: 1 },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForCaptureProcessingParams>>>(
    (app, event) => {
      return app.get(PaymentController).processCapture(event);
    },
  ),
);

/**
 * Function to process sale
 */
export const postSaleProcessing = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.POST_SALE_PROCESSING, minInstances: 1 },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForSaleProcessingParams>>>(
    (app, event) => {
      return app.get(PaymentController).processPostSale(event);
    },
  ),
);

/**
 * Function to process void
 */
export const voidProcessing = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.VOID_PROCESSING, minInstances: 1 },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForVoidProcessingParams>>>(
    (app, event) => {
      return app.get(PaymentController).processVoid(event);
    },
  ),
);

/**
 * Function to process copy trip to driver in firebase
 */
export const copyTripToDriverProcessing = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.COPY_TRIP_TO_DRIVER_PROCESSING, timeoutSeconds: 180 },
  nestServiceInstance.listenerWrapper<
    CloudEvent<MessagePublishedData<PublishMessageForCopyTripToDriverProcessingParams>>
  >((app, event) => {
    app.get(MerchantDriverController).copyToDriverCollectionInFireStore(event);
  }),
);

/**
 * Function to process a driver trip change
 */
export const driverTripChangedNest = onDocumentWritten(
  { ...defaultNestOptions, document: "drivers/{driverId}/sessions/{sessionId}/trips/{tripId}" },
  nestServiceInstance.listenerWrapper<
    FirestoreEvent<
      Change<DocumentSnapshot> | undefined,
      ParamsOf<"drivers/{driverId}/sessions/{sessionId}/trips/{tripId}">
    >
  >(async (app, event) => {
    return app.get(MerchantDriverController).driverTripChanged(event);
  }),
);

/**
 * Function to process identity activities
 */
export const identityProcessing = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.IDENTITY_PROCESSING },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForIdentityProcessingParams>>>(
    (app, event) => {
      const data = event.data.message.json;
      loggerUtils.info("identityProcessing ", data);
      return app.get(IdentityController).processIdentityEvent(event);
    },
  ),
);

/**
 * Function to process a user change
 */
export const userUpdated = onDocumentWritten(
  { ...defaultNestOptions, document: "users/{userId}" },
  nestServiceInstance.listenerWrapper<FirestoreEvent<Change<DocumentSnapshot> | undefined, ParamsOf<"users/{userId}">>>(
    (app, event) => {
      return app.get(UserController).userProfileChangedInFirestore(event);
    },
  ),
);

/**
 * Function to process direct sale
 */
export const directSaleProcessing = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.DIRECT_SALE_PROCESSING, minInstances: 1 },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForDirectSaleProcessingParams>>>(
    (app, event) => {
      app.get(PaymentController).processDirectSale(event);
    },
  ),
);

/**
 * Function triggered after successful user authentication to block login
 */
export const userSignedIn = beforeUserSignedIn(
  { ...defaultNestOptions },
  nestServiceInstance.listenerWrapper<AuthBlockingEvent>((app, event) => {
    return app.get(AuthController).processSuccessfulAuth(event);
  }),
);

/**
 * Function to listen to a Firebase publishing a new fatal issue
 */
export const sendFatalCrashToTeams = onNewFatalIssuePublished(
  { ...defaultNestOptions, minInstances: 1 },
  nestServiceInstance.listenerWrapper<CrashlyticsEvent<NewFatalIssuePayload>>(async (app, event) => {
    return app.get(MessageTeamsController).sendFirestoreFatalEventsToTeams(event);
  }),
);

/**
 * Function to process the dead letter queue
 */
export const deadLetterQueue = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.DEAD_LETTER_QUEUE, minInstances: 1 },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<DeadLetterQueueMessage>>>(async (app, event) => {
    app.get(MessageTeamsController).sendBackendFatalEventsToTeams(event);
  }),
);

/**
 * Function to add a transaction event for the system
 * right now, it is only used for HailOrderRequestTimeout(until 25/04/2025)
 */
export const addTxEventForSystem = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.ADD_TX_EVENT_FOR_SYSTEM, minInstances: 1 },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForTxEventSystemParams>>>(
    (app, event) => {
      const content = event.data.message.json;
      const [validatedData] = ValidationService.validate<[PublishMessageForTxEventSystemParams]>([
        { value: content, schema: publishMessageForTxEventSystemParamsSchema },
      ]);
      return app.get(SystemTransactionService).addEvent(validatedData.transactionId, validatedData.content);
    },
  ),
);

/**
 * Function to process vehicle heartbeats
 */
export const heartbeatProcessing = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.HEARTBEAT_CHANGE_PROCESSING },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForHeartbeatProcessing>>>(
    (app, event) => {
      const content = event.data.message.json;
      loggerUtils.debug("heartbeatProcessing", { messageId: event.data.message.messageId });
      const [validatedData] = ValidationService.validate<[PublishMessageForHeartbeatProcessing]>([
        { value: content, schema: publishMessageForHeartbeatProcessingSchema },
      ]);
      return app.get(MeterController).processMeterHeartbeat(validatedData);
    },
  ),
);

/**
 * Function to emit webhook messages
 */
export const webhookMessageProcessing = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.WEBHOOK_MESSAGE_PROCESSING },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForWebhookProcessing>>>(
    (app, event) => {
      const content = event.data.message.json;
      loggerUtils.debug("webhookMessageProcessing", { messageId: event.data.message.messageId });
      const [validatedData] = ValidationService.validate<[PublishMessageForWebhookProcessing]>([
        { value: content, schema: publishMessageForWebhookProcessingSchema },
      ]);
      return app.get(WebhookController).processWebhookMessage(validatedData);
    },
  ),
);

/**
 * Function to process push-notification requesting from the other backends
 */
export const pushNotificationProcessing = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.PUSH_NOTIFICATION_PROCESSING },
  nestServiceInstance.listenerWrapper<
    CloudEvent<MessagePublishedData<PublishMessageForPushNotificationProcessingParams>>
  >((app, event) => {
    const data = event.data.message.json;
    loggerUtils.info("pushNotificationProcessing ", data);
    return app.get(FcmService).processPushNotification(event);
  }),
);

/**
 * Function to run a cron job, every minute
 */
export const checkReportJobScheduler = onSchedule(
  {
    schedule: "* * * * *",
    timeZone: "Asia/Hong_Kong",
    region: "asia-east2",
    minInstances: 1,
    maxInstances: 1,
    memory: "512MiB",
  },
  nestServiceInstance.listenerWrapper<ScheduledEvent>((app, event) => {
    const now = new Date();
    return app.get(ReportJobController).checkReportJob(now);
  }),
);

/**
 * Function to handle report job messages
 */
export const reportJobProcessing = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.REPORT_JOB_PROCESSING, minInstances: 1 },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForReportJobProcessingParams>>>(
    (app, event) => {
      return app.get(ReportJobController).processReportJob(event);
    },
  ),
);

/**
 * Function to process campaign event
 */
export const campaignTriggerProcessing = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.CAMPAIGN_TRIGGER_PROCESSING },
  nestServiceInstance.listenerWrapper<
    CloudEvent<MessagePublishedData<PublishMessageForCampaignTriggerProcessingParams>>
  >((app, event) => {
    return app.get(CampaignService).processCampaignTrigger(event);
  }),
);

export const hailingTxCreated = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.HAILING_TX_CREATED },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForHailingTxCreatedParams>>>(
    (app, event) => {
      return app.get(TeamOrderNotificationController).orderCreatedNotification(event.data.message.json.txId);
    },
  ),
);

export const hailingStatusChanged = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.HAILING_STATUS_CHANGED },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForHailingStatusChangedParams>>>(
    (app, event) => {
      return app
        .get(TeamOrderNotificationController)
        .orderStatusChangedNotification(event.data.message.json.txId, event.data.message.json.status);
    },
  ),
);

export const pickupReminder = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.PICKUP_REMINDER },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForPickupReminderParams>>>(
    (app, event) => {
      return app.get(TeamOrderNotificationController).schedulePickupReminderNotification(event.data.message.json.txId);
    },
  ),
);

export const tripEndEvent = onMessagePublished(
  { ...defaultNestOptions, topic: PubSubService.TOPIC_NAMES.TRIP_END_EVENT },
  nestServiceInstance.listenerWrapper<CloudEvent<MessagePublishedData<PublishMessageForTripEndEventParams>>>(
    (app, event) => {
      return app.get(TripController).handleTripEndSideEffect(event.data.message.json.txId);
    },
  ),
);

/**
 * Function to run a cron job, every 5 minute
 */
export const voidTxJobScheduler = onSchedule(
  {
    schedule: "*/5 * * * *",
    timeZone: "Asia/Hong_Kong",
    region: "asia-east2",
    minInstances: 1,
    maxInstances: 10,
    memory: "512MiB",
  },
  nestServiceInstance.listenerWrapper<ScheduledEvent>((app, event) => {
    const now = new Date();
    return app.get(PaymentService).processVoidJobs(now);
  }),
);
