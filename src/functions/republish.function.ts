import "dotenv/config"; // Load environment variables into process.env

import { CloudEvent, CloudFunction } from "firebase-functions/v2";
import { MessagePublishedData, onMessagePublished } from "firebase-functions/v2/pubsub";
import { apply } from "json-logic-js";
import { createClient } from "redis";

import loggerUtils from "@nest/modules/utils/utils/logger.utils";

import { smallInstanceOptions, withHailingVpcOptions } from "./_defaultOptions";

export type Pubsub2RedisRule = {
  name: string;
  pubsubTopic: string;
  redisTopic: string;
  radius: string;
  filter: string; // JsonLogic filter as a string
};

export const republishEventToRedis = async (topic: string, data: any) => {
  // REDIS_PUBSUB_URL doesn't have sensitive info. it's just internal IP and port.
  const redisClient = createClient({
    url: process.env.REDIS_PUBSUB_URL,
    database: process.env.REDIS_PUBSUB_DB ? parseInt(process.env.REDIS_PUBSUB_DB, 10) : 2,
  });

  // fail if we cannot connect. Cloud Function will handle logging.
  await redisClient.connect();

  try {
    await redisClient.publish(topic, JSON.stringify(data));
    loggerUtils.info("[republishEventToRedis] republish successful", {
      topic,
      data,
      url: process.env.REDIS_PUBSUB_URL,
      database: process.env.REDIS_PUBSUB_DB,
    });
  } catch (error) {
    loggerUtils.error(
      "[republishEventToRedis] Error republishing event to Redis",
      {
        topic,
        data,
        url: process.env.REDIS_PUBSUB_URL,
        database: process.env.REDIS_PUBSUB_DB,
      },
      error as Error,
    );
  } finally {
    // Ensure the Redis client is closed after publishing
    await redisClient.close();
  }
};

export const createEventHandler = (rule: Pubsub2RedisRule): CloudFunction<CloudEvent<MessagePublishedData<any>>> => {
  return onMessagePublished(
    { ...smallInstanceOptions, ...withHailingVpcOptions, topic: rule.pubsubTopic },
    async (event) => {
      const data = event.data.message.json;
      const radius = rule.radius || "1000"; // default to 1000 if not specified
      const filter = JSON.parse(rule.filter || "{}"); // default to empty object if not specified

      if (apply(filter, data) === false) {
        loggerUtils.debug("[createEventHandler/onMessagePublished] Event filtered out by rule", {
          rule,
          data,
          filter,
        });
        return; // Skip processing if the filter condition is not met
      }

      try {
        await republishEventToRedis(rule.redisTopic, { data, radius, filter });
        loggerUtils.debug("[createEventHandler/onMessagePublished] Republished event to Redis", {
          rule,
          data,
        });
      } catch (e) {
        loggerUtils.error(
          "[createEventHandler/onMessagePublished] Error republishing event to Redis",
          { rule, data },
          e as Error,
        );
      }
    },
  );
};
