import { RuntimeOptions } from "firebase-functions/v1";
import { HttpsOptions } from "firebase-functions/v2/https";
import { GlobalOptions } from "firebase-functions/v2/options";

/**
 * Default options for all functions
 * Concurency is 80 by default for all functions
 */
export const defaultRuntimeOptionsGen1: RuntimeOptions = {
  minInstances: 2,
};

export const defaultGlobalOptionsGen2: GlobalOptions = {
  minInstances: 2,
  region: "asia-east2",
  maxInstances: 10,
};

export const defaultHttpsOptionsGen2: HttpsOptions = {
  ...defaultGlobalOptionsGen2,
  invoker: "public",
};

export const defaultNestOptions: GlobalOptions = {
  ...defaultGlobalOptionsGen2,
  memory: "512MiB",
};

export const withHailingVpcOptions: GlobalOptions = {
  vpcConnector: "hail-connector",
  vpcConnectorEgressSettings: "PRIVATE_RANGES_ONLY",
};

export const smallInstanceOptions: GlobalOptions = {
  // ...defaultNestOptions,
  ...defaultGlobalOptionsGen2,
  minInstances: 1,
  // memory: "128MiB",
};

export const defaultNestHttpsOptions: HttpsOptions = { ...defaultHttpsOptionsGen2, ...defaultNestOptions };
