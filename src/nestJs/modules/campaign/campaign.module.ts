import { forwardRef, <PERSON>du<PERSON> } from "@nestjs/common";

import { CampaignRepository } from "../database/repositories/campaign.repository";
import { DiscountRepository } from "../database/repositories/discount.repository";
import { JsonLogicModule } from "../jsonLogic/jsonLogic.module";
import { UserModule } from "../user/user.module";

import { CampaignService } from "./campaign.service";

@Module({
  providers: [CampaignRepository, CampaignService, DiscountRepository],
  imports: [forwardRef(() => UserModule), JsonLogicModule],
  controllers: [],
  exports: [CampaignService],
})
export class CampaignModule {}
