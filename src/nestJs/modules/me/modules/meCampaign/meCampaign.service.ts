import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { DecodedIdToken } from "firebase-admin/auth";

import { CampaignService } from "@nest/modules/campaign/campaign.service";
import { CampaignSponsorType } from "@nest/modules/campaign/dto/campaign.dto";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { RuleParams } from "@nest/modules/jsonLogic/dto/ruleParams.dto";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import {
  CampaignClaimResponse,
  CampaignGetIssuedResponse,
  CampaignQueryApplicableBody,
  CampaignQueryApplicableResponse,
} from "./dto/meCampaign.dto";

@Injectable()
export class MeCampaignService {
  constructor(
    private readonly campaignService: CampaignService,
    @InjectRepository(UserRepository) private readonly userRepository: UserRepository,
  ) {}

  async queryApplicable(
    body: CampaignQueryApplicableBody,
    user: DecodedIdToken,
  ): Promise<CampaignQueryApplicableResponse> {
    const params = body.params;
    if (!params.userId) {
      const foundUser = await this.userRepository.findOne({ where: { appDatabaseId: user.uid } });
      if (!foundUser) {
        throw errorBuilder.user.notFoundInSql(user.uid);
      }
      params.userId = foundUser.id;
    }

    const ruleParams = RuleParams.fromJson(params);
    const campaignThirdParty = await this.campaignService.getApplicableCampaign(
      ruleParams,
      CampaignSponsorType.THIRD_PARTY,
    );
    const campaignDash = await this.campaignService.getApplicableCampaign(ruleParams, CampaignSponsorType.DASH);

    return {
      campaignIdThirdParty: campaignThirdParty?.id,
      campaignRulesThirdParty: campaignThirdParty?.discountRules,
      campaignIdDash: campaignDash?.id,
      campaignRulesDash: campaignDash?.discountRules,
    };
  }

  async getCampaignsList(user: DecodedIdToken): Promise<CampaignGetIssuedResponse> {
    const { campaigns, totalRedeemedValue, totalAvailableValue } =
      await this.campaignService.getCampaignsListWithAggregatedValues(user.uid);
    return { campaigns, totalRedeemedValue, totalAvailableValue };
  }

  async claim(rewardCode: string, user: DecodedIdToken): Promise<CampaignClaimResponse> {
    const discount = await this.campaignService.claimDiscount(rewardCode, user.uid);

    return { discount };
  }
}
