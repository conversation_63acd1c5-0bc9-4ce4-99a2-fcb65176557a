import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import Campaign from "@nest/modules/database/entities/campaign.entity";
import Discount from "@nest/modules/database/entities/discount.entity";
import { RuleParams, ruleParamsSchema } from "@nest/modules/jsonLogic/dto/ruleParams.dto";

export class CampaignQueryApplicableBody {
  @ApiProperty({
    type: "object",
    example: {
      userId: "2cb227e0-cf7f-46f9-b760-cde1565d902b",
      transactionType: "TRIP",
      transactionSubtype: "HAILING",
      paymentInstrumentType: "VISA",
      paymentChannel: "APP",
    },
    description: "Campaign applicable params to be passed into jsonLogic",
  })
  params: RuleParams;
}

export const campaignQueryApplicableBodySchema = Joi.object<CampaignQueryApplicableBody>({
  params: ruleParamsSchema.required(),
});

export type CampaignQueryApplicableResponse = {
  campaignIdThirdParty?: string;
  campaignRulesThirdParty?: string;
  campaignIdDash?: string;
  campaignRulesDash?: string;
};

export class CampaignGetIssuedResponse {
  @ApiProperty({
    type: "array",
    description: "List of campaigns",
  })
  campaigns: Campaign[];
  @ApiProperty({
    type: "number",
    description: "Total redeemed value",
  })
  totalRedeemedValue: number;
  @ApiProperty({
    type: "number",
    description: "Total available value",
  })
  totalAvailableValue: number;
}

export class CampaignClaimBody {
  @ApiProperty({ type: "string", example: "abc123", description: "Reward code", required: true })
  rewardCode: string;
}

export const campaignClaimBodySchema = Joi.object<CampaignClaimBody>({
  rewardCode: Joi.string().required(),
});

export class CampaignClaimResponse {
  @ApiProperty()
  discount: Discount;
}
