import { Inject, Injectable } from "@nestjs/common";
import { polygon, point, booleanPointInPolygon } from "@turf/turf";
import * as jsonLogic from "json-logic-js";

import { LocationService } from "../location/location.service";
import { RuleParams } from "./dto/ruleParams.dto";
import { LocationLanguage } from "../location/dto/location.dto";

@Injectable()
export class JsonLogicService {
  constructor(@Inject(LocationService) private readonly locationService: LocationService) {
    this.initialize();
  }

  async initialize(): Promise<void> {
    this.registerPipLogic(jsonLogic);
    this.registerTimeLogic(jsonLogic);
  }

  private registerPipLogic(engine: typeof jsonLogic): void {
    engine.add_operation("point_in_polygon", (latlng: [number, number] | null, polygonCoords: [number, number][]) => {
      // Ensure polygon is closed
      if (latlng === null) {
        return false;
      }
      const coords = polygonCoords.slice();
      const first = coords[0];
      const last = coords[coords.length - 1];
      if (first[0] !== last[0] || first[1] !== last[1]) {
        coords.push(first);
      }
      const poly = polygon([coords]);
      const pt = point(latlng);
      return booleanPointInPolygon(pt, poly);
    });
  }

  private registerTimeLogic(engine: typeof jsonLogic): void {
    engine.add_operation(
      "time_between",
      (
        now: string | null,
        ranges: [string, string][] | string, // Accepts array of ranges or single range
        end?: string,
      ) => {
        if (now === null) {
          return false;
        }
        const toMinutes = (t: string): number => {
          const [h, m] = t.split(":").map(Number);
          return h * 60 + m;
        };
        const current = toMinutes(now);
        // Helper to check if current is in a range, supporting overnight
        const inRange = (s: number, e: number) => {
          if (s <= e) {
            return current >= s && current <= e;
          } else {
            // Overnight range (e.g., 23:00-02:00)
            return current >= s || current <= e;
          }
        };
        if (Array.isArray(ranges) && Array.isArray(ranges[0])) {
          return (ranges as [string, string][]).some(([start, end]) => {
            const s = toMinutes(start);
            const e = toMinutes(end);
            return inRange(s, e);
          });
        } else {
          const s = toMinutes(ranges as string);
          const e = toMinutes(end as string);
          return inRange(s, e);
        }
      },
    );
  }

  apply(logic: any, ruleParams: RuleParams): any {
    return jsonLogic.apply(logic, ruleParams.toJson());
  }

  // Not everywhere that uses jsonlogic needs to have coordinates, so separate it as it is both async and has more overhead
  async applyWithCoordinatesCheck(logic: any, ruleParams: RuleParams): Promise<any> {
    // If ruleParams have origin place id but not coordinates, fetch coordinates from LocationService
    if (!ruleParams.origin && ruleParams.originPlaceId) {
      const placeDetails = await this.locationService.getPlaceDetails({
        placeId: ruleParams.originPlaceId,
        language: LocationLanguage.ZHHK,
      });
      if (placeDetails && placeDetails.lat && placeDetails.lng) {
        ruleParams.origin = [placeDetails.lat, placeDetails.lng];
      }
    }
    if (!ruleParams.destination && ruleParams.destinationPlaceId) {
      const placeDetails = await this.locationService.getPlaceDetails({
        placeId: ruleParams.destinationPlaceId,
        language: LocationLanguage.ZHHK,
      });
      if (placeDetails && placeDetails.lat && placeDetails.lng) {
        ruleParams.destination = [placeDetails.lat, placeDetails.lng];
      }
    }

    return jsonLogic.apply(logic, ruleParams.toJson());
  }
}
