import Joi from "joi";

import {
  PaymentChannelType,
  paymentChannelTypeSchema,
  PaymentInstrumentType,
  paymentInstrumentTypeSchema,
} from "@nest/modules/campaign/dto/campaign.dto";
import { VehicleClass } from "@nest/modules/fleet/dto/fleet.dto";

import { TxTypes, SubTxTypes } from "../../transaction/dto/txType.dto";

export class RuleParams {
  userId?: string;
  transactionType?: TxTypes;
  transactionSubtype?: SubTxTypes;
  paymentInstrumentType?: PaymentInstrumentType;
  paymentChannel?: PaymentChannelType;
  origin?: [number, number];
  destination?: [number, number];
  originPlaceId?: string;
  destinationPlaceId?: string;
  timeOfDay?: string;
  dayOfWeek?: number;
  fare?: number;
  estimatedFare?: number;
  vehicleClass?: VehicleClass;

  constructor({
    userId,
    transactionType,
    transactionSubtype,
    paymentInstrumentType,
    paymentChannel,
    origin,
    destination,
    originPlaceId,
    destinationPlaceId,
    timeOfDay,
    dayOfWeek,
    fare,
    estimatedFare,
    vehicleClass,
  }: {
    userId?: string;
    transactionType?: TxTypes;
    transactionSubtype?: SubTxTypes;
    paymentInstrumentType?: PaymentInstrumentType;
    paymentChannel?: PaymentChannelType;
    origin?: [number, number];
    destination?: [number, number];
    originPlaceId?: string;
    destinationPlaceId?: string;
    timeOfDay?: string;
    dayOfWeek?: number;
    fare?: number;
    estimatedFare?: number;
    vehicleClass?: VehicleClass;
  } = {}) {
    this.userId = userId;
    this.transactionType = transactionType;
    this.transactionSubtype = transactionSubtype;
    this.paymentInstrumentType = paymentInstrumentType;
    this.paymentChannel = paymentChannel;
    this.origin = origin;
    this.destination = destination;
    this.originPlaceId = originPlaceId;
    this.destinationPlaceId = destinationPlaceId;
    this.timeOfDay = timeOfDay;
    this.dayOfWeek = dayOfWeek;
    this.fare = fare;
    this.estimatedFare = estimatedFare;
    this.vehicleClass = vehicleClass;
  }

  static fromJson(json: any): RuleParams {
    const params = new RuleParams();
    params.userId = json.userId;
    params.transactionType = json.transactionType ? TxTypes[json.transactionType as TxTypes] : undefined;
    params.transactionSubtype = json.transactionSubtype ? SubTxTypes[json.transactionSubtype as SubTxTypes] : undefined;
    params.paymentInstrumentType = json.paymentInstrumentType
      ? PaymentInstrumentType[json.paymentInstrumentType as PaymentInstrumentType]
      : undefined;
    params.paymentChannel = json.paymentChannel
      ? PaymentChannelType[json.paymentChannel as PaymentChannelType]
      : undefined;
    params.origin = json.origin ? [json.origin[0], json.origin[1]] : undefined;
    params.destination = json.destination ? [json.destination[0], json.destination[1]] : undefined;
    params.originPlaceId = json.originPlaceId;
    params.destinationPlaceId = json.destinationPlaceId;
    params.timeOfDay = json.timeOfDay;
    params.dayOfWeek = json.dayOfWeek;
    params.fare = json.fare;
    params.estimatedFare = json.estimatedFare;
    params.vehicleClass = json.vehicleClass ? VehicleClass[json.vehicleClass as VehicleClass] : undefined;
    return params;
  }

  toJson(): Record<string, any> {
    return {
      userId: this.userId,
      transactionType: this.transactionType,
      transactionSubtype: this.transactionSubtype,
      paymentInstrumentType: this.paymentInstrumentType,
      paymentChannel: this.paymentChannel,
      origin: this.origin,
      destination: this.destination,
      originPlaceId: this.originPlaceId,
      destinationPlaceId: this.destinationPlaceId,
      timeOfDay: this.timeOfDay,
      dayOfWeek: this.dayOfWeek,
      fare: this.fare,
      estimatedFare: this.estimatedFare,
      vehicleClass: this.vehicleClass,
    };
  }
}

export const ruleParamsSchema = Joi.object<RuleParams>({
  userId: Joi.string().uuid().allow(null).optional(),
  transactionType: Joi.string<TxTypes>()
    .valid(...Object.values(TxTypes))
    .allow(null)
    .optional(),

  transactionSubtype: Joi.string<SubTxTypes>()
    .valid(...Object.values(SubTxTypes))
    .allow(null)
    .optional(),
  paymentInstrumentType: paymentInstrumentTypeSchema.allow(null).optional(),
  paymentChannel: paymentChannelTypeSchema.allow(null).optional(),
  origin: Joi.array().items(Joi.number()).length(2).allow(null).optional(),
  destination: Joi.array().items(Joi.number()).length(2).allow(null).optional(),
  originPlaceId: Joi.string().allow(null).optional(),
  destinationPlaceId: Joi.string().allow(null).optional(),
  timeOfDay: Joi.string()
    .pattern(/^\d{2}:\d{2}$/)
    .allow(null)
    .optional(),
  dayOfWeek: Joi.number().integer().min(1).max(7).allow(null).optional(),
  fare: Joi.number().min(0).allow(null).optional(),
  estimatedFare: Joi.number().min(0).allow(null).optional(),
  vehicleClass: Joi.string<VehicleClass>()
    .valid(...Object.values(VehicleClass))
    .allow(null)
    .optional(),
});
