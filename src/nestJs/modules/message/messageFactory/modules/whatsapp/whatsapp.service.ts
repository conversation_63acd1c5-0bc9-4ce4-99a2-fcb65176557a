import { HttpService } from "@nestjs/axios";
import { Inject, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { HttpsAgent } from "agentkeepalive";
import { AxiosError, isAxiosError } from "axios";
import axiosRetry, { exponentialDelay } from "axios-retry";
import _ from "lodash";
import { catchError, firstValueFrom } from "rxjs";

import { AppDatabaseService } from "@nest/modules/appDatabase/appDatabase.service";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

import { MessageRepository } from "../../../../database/repositories/message.repository";
import { PublishMessageForMessageProcessingParams } from "../../../../pubsub/dto/publishMessageForMessageProcessing.dto";
import { PubSubService } from "../../../../pubsub/pubsub.service";
import { errorBuilder } from "../../../../utils/utils/error.utils";
import { LanguageOption } from "../../../../validation/dto/language.dto";
import { ChannelTypes } from "../../../dto/channelType.dto";
import { MessageParams } from "../../../dto/messageParams.dto";
import { isPhoneRecipient } from "../../../dto/recipientType.dto";
import { TemplateTypesText } from "../../../dto/templateType.dto";
import MessageFactoryInterface from "../../messageFactoryInterface";

const receiptTemplates: Record<LanguageOption, string> = {
  [LanguageOption.ZHHK]: "receipt_zh_v4",
  [LanguageOption.EN]: "receipt_en_v4",
};
/**
 * Whatsapp service
 */
@Injectable()
export class WhatsappServices implements MessageFactoryInterface {
  public discountTemplates: Record<LanguageOption, string>;
  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly messageRepository: MessageRepository,
    private pubsubService: PubSubService,
    private readonly appDatabaseService: AppDatabaseService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {
    this.discountTemplates = {
      [LanguageOption.ZHHK]:
        "discount_" + this.configService.getOrThrow<string>("GCLOUD_PROJECT").replace(/-/g, "_") + "_zh",
      [LanguageOption.EN]:
        "discount_" + this.configService.getOrThrow<string>("GCLOUD_PROJECT").replace(/-/g, "_") + "_en",
    };
  }

  /**
   * Implement the method defined in MessageFactoryServiceInterface.
   * Process Message in WHATSAPP Service.
   * @param message PublishMessageForMessageProcessingParams
   * @returns saved Message
   */
  readonly processMessage = async (message: PublishMessageForMessageProcessingParams) => {
    if (message.channel !== ChannelTypes.WHATSAPP) {
      throw errorBuilder.message.factoryWrongImplementation({
        location: "WhatsappServices/processMessage/channel",
        message,
      });
    }
    if (!isPhoneRecipient(message.recipient)) {
      throw errorBuilder.message.whatsapp.recipientRequired(message.tranId);
    }
    let id: string;
    switch (message.template) {
      case TemplateTypesText.RECEIPT:
        try {
          id = await this.sendReceipt(message.recipient.phone, message.language, message.params);
        } catch (error: any) {
          this.logger.error("Error sending receipt to whatsapp", error);
          const newMessage: PublishMessageForMessageProcessingParams = Object.assign(message);
          newMessage.metadata.createdAt = new Date(message.metadata.createdAt);
          newMessage.channel = ChannelTypes.SMS;
          await this.pubsubService.publishMessageForMessageProcessingParams(newMessage);
          throw error;
        }
        break;
      case TemplateTypesText.DISCOUNT:
        id = await this.sendDiscount(message.recipient.phone, message.language, message.params);
        break;
      case TemplateTypesText.DRIVER_APPLICATION_APPROVED:
        id = await this.sendApplicationApproved(message.recipient.phone);
        break;
      case TemplateTypesText.FIRST_HAIL_ORDER:
        id = await this.sendFirstHailOrder(message.recipient.phone, message.language, message.params);
        break;
      case TemplateTypesText.FIRST_HAIL_TRIP_COMPLETED:
        id = await this.sendFirstHailTripComplete(message.recipient.phone, message.language);
        break;
      default:
        throw errorBuilder.message.whatsapp.templateNotImplemented(message);
    }
    return this.messageRepository.saveMessageForPhoneRecipient(message, id);
  };

  /**
   *Send Receipt Via Whatsapp.
   * @param {string} phoneNumber
   * @param {LanguageOption} language
   * @param {MessageParams} params
   * @return {promises} message id
   */
  async sendReceipt(phoneNumber: string, language: LanguageOption, params: MessageParams): Promise<string> {
    if (!params.receiptLink || !params.licensePlate || !params.tripTotal || !params.tripEndTime) {
      throw errorBuilder.global.requiredParam("'receiptLink', 'licensePlate', 'tripTotal' or 'tripEndTime'");
    }
    const template = receiptTemplates[language];
    return this.sendWhatsappMessage(phoneNumber, template, this.messageParamsToWatiParams(params));
  }

  /**
   * Send Discount Link Via Whatsapp.
   * @param {string} phoneNumber
   * @param {LanguageOption} language
   * @param {MessageParams} params
   * @return {promises} message id
   */
  async sendDiscount(phoneNumber: string, language: LanguageOption, params: MessageParams): Promise<string> {
    if (!params.id || !params.campaignName || !params.discount) {
      throw errorBuilder.global.requiredParam("'id', 'campaignName' or 'discount'");
    }
    params.pp = "https://www.d-ash.com/legal";
    params.tc = "https://www.d-ash.com/legal";
    const template = this.discountTemplates[language];
    return this.sendWhatsappMessage(phoneNumber, template, this.messageParamsToWatiParams(params));
  }

  /**
   * Send application approved message via to driver.
   * @param {string} phoneNumber
   * @return {promises} message id
   */
  async sendApplicationApproved(phoneNumber: string): Promise<string> {
    const watiTemplate = await this.appDatabaseService.configurationRepository().getWatiTemplates();
    if (!watiTemplate || !watiTemplate.driverApplicationApprove) {
      throw errorBuilder.message.whatsapp.templateNotImplemented({
        template: TemplateTypesText.DRIVER_APPLICATION_APPROVED,
      });
    }
    if (!watiTemplate.driverApplicationApprove.enabled) {
      throw errorBuilder.message.whatsapp.templateNotEnabled(TemplateTypesText.DRIVER_APPLICATION_APPROVED);
    }
    return this.sendWhatsappMessage(
      phoneNumber,
      watiTemplate.driverApplicationApprove.i18n.zhHK,
      this.messageParamsToWatiParams({}),
    );
  }

  async sendFirstHailOrder(phoneNumber: string, language: LanguageOption, params: MessageParams): Promise<string> {
    if (!params.amount) {
      throw errorBuilder.global.requiredParam("'amount'");
    }
    const watiTemplate = await this.appDatabaseService.configurationRepository().getWatiTemplates();
    if (!watiTemplate || !watiTemplate.firstHailOrder) {
      throw errorBuilder.message.whatsapp.templateNotImplemented({
        template: TemplateTypesText.FIRST_HAIL_ORDER,
      });
    }
    if (!watiTemplate.firstHailOrder.enabled) {
      throw errorBuilder.message.whatsapp.templateNotEnabled(TemplateTypesText.FIRST_HAIL_ORDER);
    }

    switch (language) {
      case LanguageOption.ZHHK:
        return this.sendWhatsappMessage(
          phoneNumber,
          watiTemplate.firstHailOrder.i18n.zhHK,
          this.messageParamsToWatiParams(params),
        );
      case LanguageOption.EN:
      default:
        return this.sendWhatsappMessage(
          phoneNumber,
          watiTemplate.firstHailOrder.i18n.en,
          this.messageParamsToWatiParams(params),
        );
    }
  }

  async sendFirstHailTripComplete(phoneNumber: string, language: LanguageOption): Promise<string> {
    const watiTemplate = await this.appDatabaseService.configurationRepository().getWatiTemplates();
    if (!watiTemplate || !watiTemplate.firstHailOrderComplete) {
      throw errorBuilder.message.whatsapp.templateNotImplemented({
        template: TemplateTypesText.FIRST_HAIL_TRIP_COMPLETED,
      });
    }
    if (!watiTemplate.firstHailOrderComplete.enabled) {
      throw errorBuilder.message.whatsapp.templateNotEnabled(TemplateTypesText.FIRST_HAIL_TRIP_COMPLETED);
    }

    switch (language) {
      case LanguageOption.ZHHK:
        return this.sendWhatsappMessage(
          phoneNumber,
          watiTemplate.firstHailOrderComplete.i18n.zhHK,
          this.messageParamsToWatiParams({}),
        );
      case LanguageOption.EN:
      default:
        return this.sendWhatsappMessage(
          phoneNumber,
          watiTemplate.firstHailOrderComplete.i18n.en,
          this.messageParamsToWatiParams({}),
        );
    }
  }

  /**
   * Send Whatsapp Message.
   * @param {string} whatsappNumber
   * @param {string} template
   * @param {Record<string, string>[]} params
   * @return {Promise} promise
   */
  async sendWhatsappMessage(
    whatsappNumber: string,
    template: string,
    params: Record<string, string>[],
  ): Promise<string> {
    const sanitizedNumber = whatsappNumber.replace("+", "");

    this.logger.debug(`Sending message to whatsapp: ${sanitizedNumber}`, {
      template,
      params,
      sanitizedNumber,
    });

    axiosRetry(this.httpService.axiosRef, {
      retries: 3,
      retryDelay: exponentialDelay,
      retryCondition: (error: AxiosError) => {
        // Only retry on network errors or 5xx server errors
        return !error.response || error.response.status >= 500;
      },
    });
    try {
      const response = await firstValueFrom(
        this.httpService
          .post(
            this.configService.getOrThrow("WATI_URL"),
            {
              broadcast_name: template,
              template_name: template,
              parameters: params,
            },
            {
              timeout: 10000,
              httpsAgent: new HttpsAgent({ keepAlive: true }),
              headers: {
                "User-Agent": "wati-client",
                "Accept-Encoding": "gzip",
                Authorization: "Bearer " + this.configService.getOrThrow("WATI_API_KEY"),
                "Content-Type": "application/json",
              },
              params: {
                whatsappNumber: sanitizedNumber,
              },
            },
          )
          .pipe(
            catchError((error: AxiosError) => {
              throw errorBuilder.message.whatsapp.apiFailure(error);
            }),
          ),
      );
      console.debug("Whatsapp response", response);
      if (response.status == 200 && response.data.validWhatsAppNumber) {
        return response.data.contact.id;
      } else {
        throw errorBuilder.message.whatsapp.invalidNumber(sanitizedNumber);
      }
    } catch (error) {
      console.debug("Whatsapp error", JSON.stringify(error));
      if (isAxiosError(error) && error.response?.status !== 200) {
        throw errorBuilder.message.whatsapp.maxAttemptCalled(error.response?.status ?? 500);
      } else {
        throw errorBuilder.global.unknown(error);
      }
    }
  }

  /**
   * messageParamsToWatiParams
   * @param {MessageParams} messageParams
   * @return {Record} params
   */
  messageParamsToWatiParams(messageParams: MessageParams): Record<string, string>[] {
    const params: Record<string, string>[] = [];
    _.forOwn(messageParams, function (value, key) {
      const obj = {
        name: _.snakeCase(key),
        value: value as string,
      };
      params.push(obj);
    });
    return params;
  }
}
