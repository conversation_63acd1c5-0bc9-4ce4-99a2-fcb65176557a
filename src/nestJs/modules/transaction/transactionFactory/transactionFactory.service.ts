import { Injectable } from "@nestjs/common";

import Payout from "@nest/modules/database/entities/payout.entity";
import { TxDiscounts } from "@nest/modules/discount/dto/discount.dto";

import { LockDocument } from "../../appDatabase/documents/lock.document";
import { Rating } from "../../appDatabase/documents/trip.document";
import { PayoutBankFileRow } from "../../bank/dto/payoutBankFile.dto";
import PaymentTx from "../../database/entities/paymentTx.entity";
import Tx from "../../database/entities/tx.entity";
import { errorBuilder } from "../../utils/utils/error.utils";
import { ReceiptLanguageType, TxReceipt } from "../dto/txReceipt.dto";
import { TxTypes } from "../dto/txType.dto";
import { LocalizedLanguage } from "../../location/dto/location.dto";

import { TransactionHailingService } from "./modules/transactionHailing/transactionHailing.service";
import { TripService } from "./modules/trip/trip.service";

type FactoryServicesMap = {
  service: TripService | TransactionHailingService;
};

/**
 * TransactionFactory service
 */
@Injectable()
export class TransactionFactoryService {
  /**
   * Transaction factory services map by tx type
   */
  readonly transactionFactoryServices: Record<TxTypes, FactoryServicesMap>;

  constructor(
    private readonly tripService: TripService,
    private readonly transactionHailingService: TransactionHailingService,
  ) {
    this.transactionFactoryServices = {
      [TxTypes.TX_ADJUSTMENT]: {
        service: this.tripService,
      },
      [TxTypes.TRIP]: {
        service: this.tripService,
      },
      [TxTypes.CARD_VERIFICATION]: {
        service: this.tripService,
      },
      [TxTypes.HAILING_REQUEST]: {
        service: this.transactionHailingService,
      },
    };
  }

  /**
   * check tx
   * @param tx Tx
   * @returns { tx: Tx, factory: FactoryServicesMap}
   */
  private checkTx(tx: Tx) {
    if (!tx) {
      throw errorBuilder.transaction.factoryTxRequired();
    }

    const factory = this.transactionFactoryServices[tx.type];
    if (!factory || !factory.service) {
      throw errorBuilder.transaction.factoryNotImplemented(tx.type);
    }

    return { tx, factory };
  }

  /**
   * Method where we check if the transaction has customer information, like phone number or email
   * @param tx Tx
   * @returns boolean
   */
  hasCustomerInformation(passedTx: Tx) {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.hasCustomerInformation(tx);
  }

  /**
   * send receipt
   * @param passedTx Tx
   * @returns Promise<string>
   */
  sendReceipt(passedTx: Tx, paymentTx: PaymentTx) {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.sendReceipt(tx, paymentTx);
  }

  /**
   * check if the tx is pay with Dash
   * @param passedTx Tx
   * @returns boolean
   */
  isPayWithDash(passedTx: Tx) {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.isPayWithDash(tx);
  }
  /**
   * get expiresAt for trip in driver in firestore
   * @param passedTx Tx
   * @returns Date
   */
  getExpiresAt(passedTx: Tx) {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.getExpiresAt(tx);
  }
  /**
   * triggered on pre payment capture
   * @param passedTx Tx
   * @returns Promise<Tx>
   */
  prePaymentProcess(passedTx: Tx) {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.prePaymentProcess(tx);
  }

  /**
   * triggered on post payment capture
   * @param passedTx Tx
   * @returns Promise<Tx>
   */
  postPaymentProcess(passedTx: Tx, success: boolean) {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.postPaymentProcess(tx, success);
  }

  /**
   * triggered on post cash payment capture
   * @param passedTx Tx
   * @returns Promise<Tx>
   */
  postCashPaymentProcess(passedTx: Tx) {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.postCashPaymentProcess(tx);
  }
  /**
   * triggered after each tx end
   * @param passedTx Tx
   * @returns Promise<Tx>
   */
  postTxEndProcess(passedTx: Tx) {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.postTxEndProcess(tx);
  }
  /**
   *  triggered on post adjustment capture
   * @param passedTx Tx
   * @returns Promise<Tx>
   */
  postAdjustmentProcess(passedTx: Tx) {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.postAdjustmentProcess(tx);
  }
  /**
   * calculate the trip total amount, if incorrect, will skip capture
   * @param tx Tx
   * @returns boolean
   */
  isTxCalculationCorrect(passedTx: Tx, isDirectSale: boolean = false): { result: boolean; reason: string } {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.isTxCalculationCorrect(tx, isDirectSale);
  }

  /**
   * get the trip total amount and the dash fee
   * @param passedTx Tx
   * @returns { total: number; dashFee: number }
   */
  getTxMonetary(passedTx: Tx): {
    total: number;
    dashFee: number;
    payoutAmount: number;
    billing: Record<string, any>;
  } {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.getTxMonetary(tx);
  }

  /**
   * should update tx metadata
   * @param currentTx Tx
   * @param newTx Tx
   * @returns boolean
   */
  shouldUpdateTxMetadata = (currentTx: Tx, newTx: Tx) => {
    const { tx, factory } = this.checkTx(currentTx);
    const { tx: nTx } = this.checkTx(newTx);
    return factory.service.shouldUpdateTxMetadata(tx, nTx);
  };

  /**
   * should query with all relations
   * @param tx Tx
   * @returns boolean
   */
  shouldQueryWithAllRelations = (passedTx: Tx) => {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.shouldQueryWithAllRelations(tx);
  };

  /**
   * get the trip tx ids to payout
   * @param passedTxs Tx[]
   * @returns { completed: Tx[]; failed: Tx[] }
   */
  getTxsToPayout(
    passedTxs: Tx[],
    fileContent: PayoutBankFileRow[],
    payouts: Payout[],
  ): { completed: Tx[]; failed: Tx[]; bankProcessing: Tx[] } {
    const result = passedTxs.map((passedTx) => this.checkTx(passedTx));
    const { factory } = result[0];
    const txs = result.map(({ tx }) => tx);
    return factory.service.getTxsToPayout(txs, fileContent, payouts);
  }

  /**
   * post payout process
   * @param passedTxs Tx[]
   * @returns Promise<Tx[]>
   */
  async postPayoutProcess(passedTxs: Tx[]): Promise<Tx[]> {
    const result = passedTxs.map((passedTx) => this.checkTx(passedTx));
    const factoryTxs = result.reduce<Partial<Record<TxTypes, { factory: FactoryServicesMap; txs: Tx[] }>>>(
      (factoryMap, results) => {
        const { tx } = results;
        if (factoryMap[tx.type]) {
          factoryMap[tx.type]!.txs.push(tx);
        } else {
          factoryMap[tx.type] = { factory: results.factory, txs: [tx] };
        }
        return factoryMap;
      },
      {},
    );
    const txs = await Promise.all(
      Object.values(factoryTxs).map(({ factory, txs }) => factory.service.postPayoutProcess(txs)),
    );
    return txs.flat();
  }

  /**
   * Get receipt data by tx id
   * @param tx Tx
   * @returns TxReceipt
   */
  getReceiptByTx(passedTx: Tx, language: ReceiptLanguageType): Promise<TxReceipt> {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.getReceiptByTx(tx, language);
  }

  /**
   * update metadata of a tx
   * @param tx Tx
   * @param data Record<string, any>
   * @returns Promise<Tx>
   */
  updateMetadata(passedTx: Tx, data: Record<string, any>): Promise<Tx> {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.updateMetadata(tx, data);
  }

  /**
   * unlock a tx
   * @param tx Tx
   * @param userId string
   * @returns Promise<Tx>
   */
  unlock(passedTx: Tx, userId: string): Promise<Tx> {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.unlock(tx, userId);
  }

  /**
   * lock a tx
   * @param tx Tx
   * @param userId string
   * @returns Promise<Tx>
   */
  lock(passedTx: Tx, userId: string, timeout: number): Promise<LockDocument> {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.lock(tx, userId, timeout);
  }

  /**
   * set tip from user app
   * @param tx Tx
   * @param userId string
   * @param tip number
   * @returns Promise<Tx>
   */
  setTipFromUserApp(passedTx: Tx, userId: string, tip: number): Promise<Tx> {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.setTipFromUserApp(tx, userId, tip);
  }

  /**
   * set rating from user app
   * @param tx Tx
   * @param userId string
   * @param rating number
   * @returns Promise<Tx>
   */
  setRating(passedTx: Tx, userId: string, rating: Rating): Promise<Tx> {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.setRating(tx, userId, rating);
  }

  /**
   * add to user/trip from meter/trip
   * @param tx Tx
   * @param appDatabaseId string
   * @returns Promise<Tx>
   */
  addToUserCurrentTx(passedTx: Tx, appDatabaseId: string): Promise<Tx> {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.addToUserCurrentTx(tx, appDatabaseId);
  }

  /**
   * get transaction date
   * @param tx Tx
   * @returns Date
   */
  getTxDate(passedTx: Tx): Date {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.getTxDate(tx);
  }

  /**
   * check if tx is able to pair
   * @param tx Tx
   * @returns boolean
   */
  isTxAbleToPair(passedTx: Tx): boolean {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.isTxAbleToPair(tx);
  }

  /**
   * add extra metadata to tx
   * @param tx Tx
   * @returns Record<string, any>
   */
  addExtraMetadata(passedTx: Tx, language?: LocalizedLanguage): Promise<Record<string, any>> {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.addExtraMetadata(tx, language);
  }

  /**
   * apply discount to tx
   * @param tx Tx
   */
  updateHailingTripWithDiscounts(passedTx: Tx, hailingTx?: Tx, meterId?: string) {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.updateHailingTripWithDiscounts(tx, hailingTx, meterId);
  }

  /**
   * calculate discount
   * @param tx Tx
   * @return number
   */
  calculateDiscountsAndUpdateTx(
    tx: Tx,
    discounts: TxDiscounts,
  ): Promise<{ updatedTx: Tx; isDiscountMatch: boolean; discountAmountFromFirestore?: number }> {
    const { factory } = this.checkTx(tx);
    return factory.service.calculateDiscountsAndUpdateTx(tx, discounts);
  }

  /**
   * reset or fail discounts
   */
  resetDiscountsForHailingRequest(passedTx: Tx): Promise<void> {
    const { tx, factory } = this.checkTx(passedTx);
    return factory.service.resetDiscountsForHailingRequest(tx);
  }
}
