import Payout from "@nest/modules/database/entities/payout.entity";
import { TxDiscounts } from "@nest/modules/discount/dto/discount.dto";

import { Rating } from "../../appDatabase/documents/trip.document";
import { PayoutBankFileRow } from "../../bank/dto/payoutBankFile.dto";
import PaymentTx from "../../database/entities/paymentTx.entity";
import Tx from "../../database/entities/tx.entity";
import { LocalizedLanguage } from "../../location/dto/location.dto";
import { ReceiptLanguageType, TxReceipt } from "../dto/txReceipt.dto";

/**
 * Service interface to be implemented by all payment gateway services
 */
export default interface TransactionFactoryInterface {
  /**
   * Method where we check if the transaction has customer information, like phone number or email
   * @param tx Tx
   * @returns boolean
   */
  readonly hasCustomerInformation: (tx: Tx) => boolean;
  /**
   * Send receipt
   * Method where we send the receipt to the customer
   * @param tx Tx
   * @param paymentTx PaymentTx
   * @returns Promise<string>
   */
  readonly sendReceipt: (tx: Tx, paymentTx: PaymentTx) => Promise<string>;

  /**
   * Triggered on post payment capture
   * Method where we do the post payment processing (eg: meter trip copy to trip document)
   * @param tx Tx
   * @returns Promise<Tx>
   */
  readonly postPaymentProcess: (tx: Tx, success: boolean) => Promise<Tx>;

  /**
   * Triggered on post cash payment capture
   * Method where we do the post cash payment processing (eg: meter trip copy to driver document)
   * @param tx Tx
   * @returns promise<Tx>
   */
  readonly postCashPaymentProcess: (tx: Tx) => Promise<Tx>;
  /**
   * Triggered after each tx end
   * Method where we do the post trip end processing (eg: we need copy meter trip to driver trip each time, even after the trip is ended)
   * @param passedTx Tx
   * @returns Promise<Tx>
   */
  readonly postTxEndProcess: (tx: Tx) => Promise<Tx>;
  /**
   * Triggered on post adjustment capture
   * Method where we do the post adjustment processing (eg: meter trip copy to trip document)
   * @param tx Tx
   * @returns Promise<Tx>
   */
  readonly postAdjustmentProcess: (tx: Tx) => Promise<Tx>;
  /**
   * Triggered on pre payment capture
   * Method where we set the transaction total amount
   * @param tx Tx
   * @returns Promise<Tx>
   */
  readonly prePaymentProcess: (tx: Tx) => Promise<Tx>;

  /**
   * Is Tx calculation correct
   * Method where we check if the total amount is correct
   * @param tx Tx
   * @returns boolean
   */
  readonly isTxCalculationCorrect: (tx: Tx, isDirectSale: boolean) => { result: boolean; reason: string };

  /**
   * Get Tx monetary
   * Method where we get the total amount and the dash fee
   * @param tx Tx
   * @returns { total: number; dashFee: number }
   */
  readonly getTxMonetary: (tx: Tx) => {
    total: number;
    dashFee: number;
    payoutAmount: number;
    billing: Record<string, any>;
  };

  /**
   * Should Update Tx Metadata
   * Method where we check if we should update the transaction metadata
   * @param currentTx Tx
   * @param newTx Tx
   * @returns boolean
   */
  readonly shouldUpdateTxMetadata: (currentTx: Tx, newTx: Tx) => { shouldUpdate: boolean; condition: string };

  /**
   * should query with all relations
   * @param tx Tx
   * @returns boolean
   */
  readonly shouldQueryWithAllRelations: (tx: Tx) => boolean;

  /**
   * Get Tx ids to payout
   * Method where we get the tx ids to payout
   * @param txs Tx[]
   * @returns { completed: Tx[]; failed: Tx[] }
   */
  readonly getTxsToPayout: (
    txs: Tx[],
    fileContent: PayoutBankFileRow[],
    payouts: Payout[],
  ) => { completed: Tx[]; failed: Tx[]; bankProcessing: Tx[] };

  /**
   * Get Tx ids to payout
   * Method where we get the tx ids to payout
   * @param txs Tx[]
   * @returns Tx[]
   */
  readonly postPayoutProcess: (txs: Tx[]) => Promise<Tx[]>;

  /**
   * Get receipt data by tx id
   * Method where we get the receipt data by tx id
   * @param tx Tx
   * @returns txReceipt
   */
  readonly getReceiptByTx: (tx: Tx, language: ReceiptLanguageType) => Promise<TxReceipt>;

  /**
   * update metadata of a tx
   * @param tx Tx
   * @param data Record<string, any>
   * @returns Promise<Tx>
   */
  readonly updateMetadata: (tx: Tx, data: Record<string, any>) => Promise<Tx>;

  /**
   * set tip from user app
   * @param tx Tx
   * @param userId string
   * @param tip number
   * @returns Promise<Tx>
   */
  readonly setTipFromUserApp: (tx: Tx, userId: string, tip: number) => Promise<Tx>;

  /**
   * set rating from user app
   * @param tx Tx
   * @param userId string
   * @param rating number
   * @returns Promise<Tx>
   */
  readonly setRating: (tx: Tx, userId: string, rating: Rating) => Promise<Tx>;

  /**
   * add to user/trip from meter/trip
   * @param tx Tx
   * @param appDatabaseId string
   * @returns Promise<Tx>
   */
  readonly addToUserCurrentTx: (tx: Tx, appDatabaseId: string) => Promise<Tx>;

  /**
   * get transaction date
   * @param tx Tx
   * @returns Date
   */
  readonly getTxDate: (tx: Tx) => Date;

  /**
   * check if tx is able to pair
   * @param tx Tx
   * @returns boolean
   */
  readonly isTxAbleToPair: (tx: Tx) => boolean;

  /**
   * add extra metadata to tx
   * @param tx Tx
   * @return data Record<string, any>
   */
  readonly addExtraMetadata: (tx: Tx, language?: LocalizedLanguage) => Promise<Record<string, any>>;

  /**
   * update hailing trip with discounts
   */
  readonly updateHailingTripWithDiscounts: (tx: Tx, hailingTx?: Tx, meterId?: string) => Promise<void>;

  /**
   * calculate discount
   * @param tx Tx
   * @return number
   */
  readonly calculateDiscountsAndUpdateTx: (
    tx: Tx,
    discounts: TxDiscounts,
  ) => Promise<{ updatedTx: Tx; isDiscountMatch: boolean; discountAmountFromFirestore?: number }>;

  /**
   * reset discounts for hailing request tx
   */
  readonly resetDiscountsForHailingRequest: (tx: Tx) => Promise<void>;
}
