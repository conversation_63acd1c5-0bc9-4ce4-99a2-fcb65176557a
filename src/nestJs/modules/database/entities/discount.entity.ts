import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn, Relation } from "typeorm";

import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import { DiscountState } from "../../discount/dto/discount.dto";

import Campaign from "./campaign.entity";
import { DefaultEntity } from "./defaultEntity";
import Tx from "./tx.entity";
import User from "./user.entity";

const validStateTransitions: Record<DiscountState, DiscountState[]> = {
  [DiscountState.ISSUED]: [DiscountState.APPLIED, DiscountState.REDEEMED, DiscountState.FAILED], // verify this
  [DiscountState.APPLIED]: [DiscountState.REDEEMED, DiscountState.FAILED],
  [DiscountState.REDEEMED]: [],
  [DiscountState.FAILED]: [],
};

@Entity()
@Index("campaign_tx", ["campaign", "tx"], {
  unique: true,
})
export default class Discount extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  @ApiProperty()
  id: string;

  @Column({ type: "enum", enum: DiscountState })
  @ApiProperty({ type: "enum", enum: DiscountState })
  state: DiscountState;

  @ManyToOne(() => Campaign, (campaign) => campaign.id)
  @JoinColumn()
  @ApiProperty({ type: () => Campaign })
  @Index("campaign_index_for_discount")
  campaign: Relation<Campaign>;

  @ManyToOne(() => User, (user) => user.id)
  @JoinColumn()
  @ApiProperty({ type: () => User })
  @Index("user_index_for_discount")
  user?: Relation<User>;

  @ManyToOne(() => Tx, (tx) => tx.id)
  @JoinColumn()
  @ApiProperty({ type: () => Tx })
  tx: Relation<Tx | null>;

  @Column({ type: "timestamp", nullable: true })
  @ApiProperty()
  startAt?: Date;

  @Column({ type: "timestamp", nullable: true })
  @ApiProperty()
  endAt?: Date;

  @Column({ type: "timestamp", nullable: true })
  @ApiProperty()
  redeemedAt?: Date;

  @Column({ type: "double precision", nullable: true })
  @ApiProperty()
  redeemedValue?: number | null;

  @Column("varchar", { length: 20, nullable: true, unique: true })
  @ApiProperty()
  rewardCode?: string;

  redeem() {
    this.transitionTo(DiscountState.REDEEMED);
    this.redeemedAt = new Date();

    return this;
  }

  canTransitionTo(newState: DiscountState): boolean {
    return validStateTransitions[this.state].includes(newState);
  }

  transitionTo(newState: DiscountState): Discount {
    if (!this.canTransitionTo(newState)) {
      throw errorBuilder.incentive.discount.cannotTransitionState(this.id, this.state, newState);
    }

    this.state = newState;

    return this;
  }
}
