import { Inject, Injectable } from "@nestjs/common";
import jsonMask from "json-mask";
import { EntitySubscriberInterface, EventSubscriber, InsertEvent, UpdateEvent, RemoveEvent, DataSource } from "typeorm";

import LoggerServiceAdapter from "../utils/logger/logger.service";

@Injectable()
@EventSubscriber()
export class EntityLoggerSubscriber implements EntitySubscriberInterface {
  constructor(@Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter, private dataSource: DataSource) {
    this.dataSource.subscribers.push(this);
  }

  afterLoad(entity: any) {
    const model = entity.constructor.name;
    this.logger.info(`DB Query Entity Loaded for Model: ${model}`, {
      type: "entity-load",
      model,
      action: "LOAD",
      entity: entity ? this.sanitizeResult(entity) : "unknown",
    });
  }

  afterInsert(event: InsertEvent<any>) {
    const entity = event.entity;
    const model = entity.constructor.name;

    this.logger.debug(`DB Query Entity Inserted for Model: ${model}`, {
      type: "entity-insert",
      model,
      action: "INSERT",
      entity: entity ? this.sanitizeResult(entity) : "unknown",
    });
  }

  afterUpdate(event: UpdateEvent<any>) {
    const entity = event.entity;
    const model = entity?.constructor?.name;
    this.logger.debug(`DB Query Entity Updated for Model: ${model}`, {
      type: "entity-update",
      model,
      action: "UPDATE",
      entity: entity ? this.sanitizeResult(entity) : "unknown",
    });
  }

  afterRemove(event: RemoveEvent<any>) {
    const entity = event.entity;
    const model = entity?.constructor?.name;
    this.logger.debug(`DB Query Entity Removed for Model: ${model}`, {
      type: "entity-remove",
      model,
      action: "DELETE",
      entity: entity ? this.sanitizeResult(entity) : "unknown",
    });
  }

  private sanitizeResult(result: any): any {
    if (!result) return null;

    return jsonMask(result, "password,publicKey, privateKey, tokenKraken, token, cardHolderName, instrumentIdentifier");
  }
}
