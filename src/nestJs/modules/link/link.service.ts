import { Injectable } from "@nestjs/common";

import { AppDatabaseService } from "../appDatabase/appDatabase.service";
import { CreateLinkResponseDto } from "../admin/adminLink/dto/createLinkResponse.dto";
import { LinkDocument } from "../appDatabase/documents/link.document";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { DashError, DashErrorCodes, errorBuilder } from "../utils/utils/error.utils";

import { CreateLinkRequestDto } from "./dto/createLinkRequest.dto";

@Injectable()
export class LinkService {
  constructor(private readonly appDatabaseService: AppDatabaseService, private readonly logger: LoggerServiceAdapter) {}

  async createUniqueLink(
    userId: string,
    userEmail: string,
    linkCreateDto: CreateLinkRequestDto,
  ): Promise<CreateLinkResponseDto> {
    const linkDocument: LinkDocument = {
      destination: linkCreateDto.destination,
      description: linkCreateDto.description,
      includesParams: linkCreateDto.includesParams,
      createdAt: new Date(),
      createdBy: userId,
      createdByEmail: userEmail,
      ...(linkCreateDto.expiresAt && { expiresAt: linkCreateDto.expiresAt }),
    };

    if (linkCreateDto.id) {
      const createdLinkDocument = await this.appDatabaseService
        .linkRepository()
        .createUniqueDocument(linkCreateDto.id, linkDocument);

      return { ...createdLinkDocument, id: linkCreateDto.id };
    } else {
      const maxRetries = 10;

      for (let attempt = 0; attempt < maxRetries; attempt++) {
        const id = this.generateRandomId(LinkDocument.idLength);
        try {
          const createdLinkDocument = await this.appDatabaseService
            .linkRepository()
            .createUniqueDocument(id, linkDocument);

          return { ...createdLinkDocument, id };
        } catch (error) {
          if (error instanceof DashError && error.code == DashErrorCodes.FIRESTORE_ALREADY_EXISTS) {
            this.logger.warn(`Link with id ${id} already exists. Retrying x${attempt + 1}...`);
            continue;
          } else {
            throw error;
          }
        }
      }

      throw errorBuilder.link.failedToCreateLink();
    }
  }

  async getLinkById(id: string): Promise<LinkDocument | undefined> {
    return this.appDatabaseService.linkRepository().findOneById(id);
  }

  /**
   * Generate random id with a given length
   */
  private generateRandomId(length: number): string {
    return Math.random()
      .toString(36)
      .substring(2, length + 2);
  }
}
