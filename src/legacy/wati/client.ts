import { HttpsAgent } from "agentkeepalive";
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, isAxiosError } from "axios";
import * as functions from "firebase-functions";
import { defineString } from "firebase-functions/params";
import _ from "lodash";
import { attach, RetryConfig } from "retry-axios";

// Cannot be `import` as it's not under TS root dir
export const defaultHttpsAgent = new HttpsAgent({ keepAlive: true });
export const defaultTimeout = 10000;
export const acceptEncoding = "gzip";
export const contentType = "application/json";
export const userAgent = "wati-client";
export const key = defineString("WATI_API_KEY");
export const defaultUrl = "https://live-server-11251.wati.io/api/v1/sendTemplateMessage";

export const defaultConfig: AxiosRequestConfig = {
  timeout: defaultTimeout,
  httpsAgent: defaultHttpsAgent,
  headers: {
    "User-Agent": userAgent,
    "Accept-Encoding": acceptEncoding,
    // "Authorization": "Bearer " + key.value(),
    "Content-Type": contentType,
  },
};

export type Config = {
  raxConfig?: RetryConfig;
} & AxiosRequestConfig;

export interface ClientOptions {
  config?: Config;
}

export const defaultAxiosInstance = axios.create(defaultConfig);
attach(defaultAxiosInstance);

/**
 * Client is a light wrapper around API methods providing shared
 * configuration for Axios settings such as retry logic using the
 * default retry-axios settings and gzip encoding.
 */
class WatiClient {
  private axiosInstance: AxiosInstance;

  /**
   *
   * @param {object} config
   */
  constructor({ config }: ClientOptions = {}) {
    functions.logger.info(JSON.stringify(defaultConfig.headers));
    if (config) {
      config = { ...defaultConfig, ...config };
      config.headers = { ...defaultConfig.headers, ...(config.headers || {}) };
      this.axiosInstance = axios.create(config);
      attach(this.axiosInstance);
    } else {
      this.axiosInstance = defaultAxiosInstance;
    }
  }

  /**
   * sendActivation
   * @param {string} whatsappNumber
   * @param {WhatsappTemplate} template
   * @param {Record<string, string>[]} payload
   * @return {Promise} promise
   */
  async sendWhatsappMessage(
    whatsappNumber: string,
    template: WhatsappTemplate,
    payload: WhatappMessagePayload,
  ): Promise<string> {
    functions.logger.info("sendWhatsappMessage");
    functions.logger.debug(
      "WATI Client sending message to %s, payload, %s, template %s",
      whatsappNumber,
      JSON.stringify(payloadToParams(payload)),
      template,
    );
    try {
      const response = (await this.axiosInstance.post(
        defaultUrl,
        {
          broadcast_name: template,
          template_name: template,
          parameters: payloadToParams(payload),
        },
        {
          ...defaultConfig,
          headers: {
            ...defaultConfig.headers,
            Authorization: "Bearer " + key.value(),
          },
          params: {
            whatsappNumber: whatsappNumber,
          },
        },
      )) as AxiosResponse;
      functions.logger.debug("response code %s", response.status);
      functions.logger.debug("response data %s", JSON.stringify(response.data));
      if (response.status == 200 && response.data.validWhatsAppNumber) {
        functions.logger.info("Valid number: %s", whatsappNumber);
        return "WHATSAPP:" + response.data.contact.wAid;
      } else {
        throw new Error("NOT A VALID NUMBER");
      }
    } catch (err) {
      functions.logger.error("Error calling wati");
      if (isAxiosError(err)) {
        functions.logger.error("Axios error: %s ", err);
      } else {
        functions.logger.error("Wati error: %s", err);
      }
      throw err;
    }
  }
  /**
   * sendReceipt
   * @param {string} whatsappNumber
   * @param {string} language
   * @param {ReceiptMessagePayload} payload
   * @return {Promise} promise
   */
  sendReceipt(whatsappNumber: string, language: string, payload: ReceiptMessagePayload): Promise<string> {
    let template;
    if (language == "zh-hk") {
      template = WhatsappTemplate.RECEIPT_TC;
    } else {
      template = WhatsappTemplate.RECEIPT;
    }
    return this.sendWhatsappMessage(whatsappNumber, template, payload);
  }

  /**
   * sendActivation
   * @param {string} whatsappNumber
   * @param {string} language
   * @param {ActivationMessagePayload} payload
   * @return {Promise} promise
   */
  sendActivation(whatsappNumber: string, language: string, payload: ActivationMessagePayload): Promise<string> {
    const template = WhatsappTemplate.ACTIVATION;
    return this.sendWhatsappMessage(whatsappNumber, template, payload);
  }
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface WhatappMessagePayload {}

export interface ReceiptMessagePayload extends WhatappMessagePayload {
  // these param name are from wati template
  receiptLink: string;
  licensePlate: string;
  tripEndTime: string;
  tripTotal: number;
}

export interface ActivationMessagePayload extends WhatappMessagePayload {
  activation_link_zh: string;
  activation_link_en: string;
}

/**
 * PayloadToParams
 * @param {WhatappMessagePayload} payload
 * @return {Record} params
 */
function payloadToParams(payload: WhatappMessagePayload): Record<string, string>[] {
  const params: Record<string, string>[] = [];
  _.forOwn(payload, function (value, key) {
    functions.logger.debug(key);
    const obj = {
      name: _.snakeCase(key),
      value: value,
    };
    params.push(obj);
  });
  functions.logger.debug(JSON.stringify(params));
  return params;
}

export enum WhatsappTemplate {
  RECEIPT = "receipt_en_v4",
  RECEIPT_TC = "receipt_zh_v4",
  BATCH_COMPLETE = "",
  ACTIVATION = "activation_v1",
}

export default new WatiClient();
