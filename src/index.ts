import fs from "fs";

import { PubSub, Topic } from "@google-cloud/pubsub";
import { onDocumentWritten } from "firebase-functions/v2/firestore";
import { load } from "js-yaml";

import "./nestJs/modules/utils/utils/sentry.utils";
import "module-alias/register";
import { createEventHandler, Pubsub2RedisRule } from "@functions/republish.function";
import loggerUtils from "@nest/modules/utils/utils/logger.utils";

import { defaultNestOptions } from "./functions/_defaultOptions";

export {
  api,
  meterTripChangedNest,
  meterTripCreatedNest,
  txProcessing,
  messageProcessing,
  driverUpdated,
  captureProcessing,
  postSaleProcessing,
  voidProcessing,
  copyTripToDriverProcessing,
  driverTripChangedNest,
  payoutFileProcessing,
  identityProcessing,
  userUpdated,
  directSaleProcessing,
  userSignedIn,
  sendFatalCrashToTeams,
  deadLetterQueue,
  addTxEventForSystem,
  pushNotificationProcessing,
  checkReportJobScheduler,
  heartbeatProcessing,
  webhookMessageProcessing,
  reportJobProcessing,
  campaignTriggerProcessing,
  voidTxJobScheduler,
  pickupReminder,
  hailingTxCreated,
  hailingStatusChanged,
  tripEndEvent,
} from "./functions/nest.function";

export { app } from "./functions/app.function";

export {
  meterTripChanged,
  tripChanged,
  meterChange,
  sessionChange,
  driverChange,
  driverSessionTripDelete,
  batchChange,
  bucketChange,
  batchJob,
  sessionExpireScheduler,
  startOrStopCloudSqlInstance,
} from "./functions/events.function";

const pubsubClient = new PubSub({ projectId: process.env.GCLOUD_PROJECT });
try {
  const config = load(fs.readFileSync("./firestore-trigger-config.yml", "utf8")) as {
    functions: Array<{
      name: string;
      path: string;
      pubsubTopic: string;
    }>;
  };
  if (!config || !config.functions) {
    throw new Error("No functions configuration found in config.yml");
  }

  // We'll use a dynamic exports object, though for Cloud Functions
  // you typically export them directly. Each entry in config.functions
  // will create a new exported function.
  for (const funcDefinition of config.functions) {
    const { name, path, pubsubTopic } = funcDefinition;
    if (!name || !path) {
      throw new Error(`Invalid function definition: ${JSON.stringify(funcDefinition)}`);
    }
    exports[name] = onDocumentWritten({ ...defaultNestOptions, document: path }, async (event) => {
      const before = event.data?.before.data();
      const after = event.data?.after.data();
      let eventType = "";
      if (!before && after) {
        eventType = "Created";
      } else if (before && !after) {
        eventType = "Deleted";
      } else if (before && after) {
        eventType = "Updated";
      }

      // Send to pubsub
      const data = { document: event.document, before, after };
      const dataBuffer = Buffer.from(JSON.stringify(data));
      try {
        const topic = await getTopic(pubsubTopic, pubsubClient);
        await topic
          .publishMessage({
            data: dataBuffer,
          })
          .then((messageId) => {
            loggerUtils.info("Published to PubSub", {
              messageId,
              pubsubTopic,
              data,
              eventType,
            });
            return messageId;
          });
      } catch (e) {
        loggerUtils.error(
          "Error publishing to PubSub",
          {
            error: e,
            pubsubTopic,
            data,
            eventType,
          },
          e as Error,
        );
      }
      return null;
    });
  }
} catch (e) {
  console.error(e);
}

export const getTopic = async (topicName: string, pubSubClient: PubSub) => {
  const topics = await pubSubClient.getTopics();
  const topic = topics.flat().find((t: any) => t.name?.includes(topicName));
  if (!topic || !(topic instanceof Topic)) {
    return await pubSubClient.createTopic(topicName).then(([topic]) => topic);
  }
  return topic;
};

try {
  const config = load(fs.readFileSync("./pubsub2redis.yaml", "utf8")) as {
    pubsub2redis: Array<Pubsub2RedisRule>;
  };
  if (!config || !config.pubsub2redis) {
    throw new Error("No functions configuration found in pubsub2redis.yaml");
  }

  for (const rule of config.pubsub2redis) {
    exports[rule.name] = createEventHandler(rule);
  }
} catch (e) {
  console.error(e);
}
