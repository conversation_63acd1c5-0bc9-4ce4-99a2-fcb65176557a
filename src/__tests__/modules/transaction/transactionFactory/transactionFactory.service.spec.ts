import { TripDocument } from "../../../../nestJs/modules/appDatabase/documents/trip.document";
import Merchant from "../../../../nestJs/modules/database/entities/merchant.entity";
import PaymentTx from "../../../../nestJs/modules/database/entities/paymentTx.entity";
import Tx from "../../../../nestJs/modules/database/entities/tx.entity";
import { LocalizedLanguage } from "../../../../nestJs/modules/location/dto/location.dto";
import { ReceiptLanguageType } from "../../../../nestJs/modules/transaction/dto/txReceipt.dto";
import { TxTypes } from "../../../../nestJs/modules/transaction/dto/txType.dto";
import { TransactionHailingService } from "../../../../nestJs/modules/transaction/transactionFactory/modules/transactionHailing/transactionHailing.service";
import { TripService } from "../../../../nestJs/modules/transaction/transactionFactory/modules/trip/trip.service";
import { TransactionFactoryService } from "../../../../nestJs/modules/transaction/transactionFactory/transactionFactory.service";
import { errorBuilder } from "../../../../nestJs/modules/utils/utils/error.utils";
import "../../../initTests";
import { expectedResultSavedTx } from "../../../utils/fakeData/fakeDataTx.specs.utils";
import { fakeTripData } from "../../../utils/fakeData/fakeTripData.specs.utils";

/**
 * This test is to check if the Factory service is returning the right data
 * and if the service is not implemented or the gateway type is not valid,
 * it should throw an error
 */
describe("transaction", () => {
  const mockFunction = jest.fn();

  const fakeTripService = {
    hasCustomerInformation: mockFunction,
    sendReceipt: mockFunction,
    prePaymentProcess: mockFunction,
    postPaymentProcess: mockFunction,
    isTxCalculationCorrect: mockFunction,
    getTxMonetary: mockFunction,
    isPayWithDash: mockFunction,
    getExpiresAt: mockFunction,
    postCashPaymentProcess: mockFunction,
    postTxEndProcess: mockFunction,
    postAdjustmentProcess: mockFunction,
    shouldUpdateTxMetadata: mockFunction,
    shouldQueryWithAllRelations: mockFunction,
    getTxsToPayout: mockFunction,
    postPayoutProcess: mockFunction,
    getReceiptByTx: mockFunction,
    updateMetadata: mockFunction,
    unlock: mockFunction,
    lock: mockFunction,
    setTipFromUserApp: mockFunction,
    setRating: mockFunction,
    addToUserCurrentTx: mockFunction,
    getTxDate: mockFunction,
    isTxAbleToPair: mockFunction,
    addExtraMetadata: mockFunction,
    updateHailingTripWithDiscounts: mockFunction,
    calculateDiscountsAndUpdateTx: mockFunction,
    resetDiscountsForHailingRequest: mockFunction,
  };

  const transactionFactoryService: TransactionFactoryService = new TransactionFactoryService(
    fakeTripService as unknown as TripService,
    fakeTripService as unknown as TransactionHailingService,
  );
  let fakeTx: Tx;
  const listOfTxsFunctions = ["getTxsToPayout"];
  const listOfTxsAsyncFunctions = ["postPayoutProcess"];

  beforeEach(() => {
    jest.clearAllMocks();
    fakeTx = new Tx();
    fakeTx.id = expectedResultSavedTx.id;
    fakeTx.type = expectedResultSavedTx.type;
    fakeTx.metadata = fakeTripData as unknown as TripDocument;
    fakeTx.merchant = expectedResultSavedTx.merchant as Merchant;
    fakeTx.paymentTx = expectedResultSavedTx.paymentTx.map((paymentTx) =>
      PaymentTx.fromJson(paymentTx, expectedResultSavedTx.id),
    );
  });

  describe("transactionFactory.service", () => {
    Object.getOwnPropertyNames(TransactionFactoryService.prototype)
      .filter((methodName) => !["constructor", "checkTx"].includes(methodName))
      .forEach((methodName) => {
        /**
         * Failure cases for the transaction factory service when tx is undefined
         */
        it(`should throw an error if tx is undefined for ${methodName}`, async () => {
          if (listOfTxsAsyncFunctions.includes(methodName)) {
            // @ts-ignore
            await expect(transactionFactoryService[methodName]([undefined as unknown as Tx])).rejects.toThrow(
              errorBuilder.transaction.factoryTxRequired(),
            );
          } else if (listOfTxsFunctions.includes(methodName)) {
            // @ts-ignore
            expect(() => transactionFactoryService[methodName]([undefined as unknown as Tx])).toThrow(
              errorBuilder.transaction.factoryTxRequired(),
            );
          } else {
            // @ts-ignore
            expect(() => transactionFactoryService[methodName](undefined as unknown as Tx)).toThrow(
              errorBuilder.transaction.factoryTxRequired(),
            );
          }
        });

        /**
         * Failure cases for the transaction factory service when factory is not implemented
         */
        it(`should throw an error if factory is not implemented for ${methodName}`, async () => {
          fakeTx.type = "wrongType" as unknown as TxTypes;

          if (listOfTxsAsyncFunctions.includes(methodName)) {
            // @ts-ignore
            await expect(() => transactionFactoryService[methodName]([fakeTx])).rejects.toThrow(
              errorBuilder.transaction.factoryNotImplemented(fakeTx.type),
            );
          } else if (listOfTxsFunctions.includes(methodName)) {
            // @ts-ignore
            expect(() => transactionFactoryService[methodName]([fakeTx])).toThrow(
              errorBuilder.transaction.factoryNotImplemented(fakeTx.type),
            );
          } else {
            // @ts-ignore
            expect(() => transactionFactoryService[methodName](fakeTx)).toThrow(
              errorBuilder.transaction.factoryNotImplemented(fakeTx.type),
            );
          }
        });

        /**
         * Success cases for the transaction factory service
         */
        if (methodName === "postPaymentProcess") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx, true);
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx, true);
          });
        } else if (methodName === "shouldUpdateTxMetadata") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx, fakeTx);
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx, fakeTx);
          });
        } else if (methodName === "postPayoutProcess") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName]([fakeTx]);
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith([fakeTx]);
          });
        } else if (methodName === "updateMetadata") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx, {});
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx, {});
          });
        } else if (methodName === "unlock") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx, "user123");
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx, "user123");
          });
        } else if (methodName === "lock") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx, "user123", 100);
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx, "user123", 100);
          });
        } else if (methodName === "setTipFromUserApp") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx, "user123", 15);
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx, "user123", 15);
          });
        } else if (methodName === "setRating") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx, "user123", 3);
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx, "user123", 3);
          });
        } else if (methodName === "addToUserCurrentTx") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx, "user123");
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx, "user123");
          });
        } else if (methodName === "getReceiptByTx") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx, ReceiptLanguageType.ENHK);
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx, ReceiptLanguageType.ENHK);
          });
        } else if (methodName === "addExtraMetadata") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx, LocalizedLanguage.EN);
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx, LocalizedLanguage.EN);
          });
        } else if (methodName === "isTxCalculationCorrect") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx);
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx, false);
          });
        } else if (methodName === "calculateDiscountsAndUpdateTx") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx, "json-logic rule");
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx, "json-logic rule");
          });
        } else if (methodName === "updateHailingTripWithDiscounts") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            const hailingTx = new Tx();
            const meterId = "fake_meter_id";
            transactionFactoryService[methodName](fakeTx, hailingTx, meterId);
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx, hailingTx, meterId);
          });
        } else if (methodName === "resetDiscountsForHailingRequest") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx);
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx);
          });
        } else if (methodName === "postTxEndProcess") {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx);
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith(fakeTx);
          });
        } else if (listOfTxsFunctions.includes(methodName)) {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName]([fakeTx], {}, []);
            expect(mockFunction).toHaveBeenCalledTimes(1);
            expect(mockFunction).toHaveBeenCalledWith([fakeTx], {}, []);
          });
        } else {
          it(`should call the mock with the tx for ${methodName}`, () => {
            // @ts-ignore
            transactionFactoryService[methodName](fakeTx, fakeTx.paymentTx);
            expect(mockFunction).toHaveBeenCalledTimes(1);

            if (methodName === "sendReceipt") {
              expect(mockFunction).toHaveBeenCalledWith(fakeTx, fakeTx.paymentTx);
            } else {
              expect(mockFunction).toHaveBeenCalledWith(fakeTx);
            }
          });
        }
      });
  });
});
