import dayjs from "dayjs";
import { Request } from "express";

import { FleetDocument } from "@nest/modules/appDatabase/documents/fleet.document";
import { MeterDocument } from "@nest/modules/appDatabase/documents/meter.document";
import { TripDocument } from "@nest/modules/appDatabase/documents/trip.document";
import { TxAppsNames } from "@nest/modules/apps/dto/Apps.dto";
import { PlatformType } from "@nest/modules/me/modules/meHailing/dto/meHailing.dto";
import { findOneByIdMock, getHailConfigMock } from "@tests/utils/services/FakeAppDatabaseService.specs.utils";
import { mockExecute } from "@tests/utils/services/FakeCancelFleetOrderDelegatee.specs.utils";
import { saveMock } from "@tests/utils/services/FakeEntityManager.specs.utils";
import { newTestMeTransactionController } from "@tests/utils/services/TestServices.specs.utils";

import TxApp from "../../../../../nestJs/modules/database/entities/app.entity";
import Merchant from "../../../../../nestJs/modules/database/entities/merchant.entity";
import PaymentTx from "../../../../../nestJs/modules/database/entities/paymentTx.entity";
import Tx from "../../../../../nestJs/modules/database/entities/tx.entity";
import TxEvent from "../../../../../nestJs/modules/database/entities/txEvent.entity";
import { MeTransactionController } from "../../../../../nestJs/modules/me/modules/meTransaction/meTransaction.controller";
import { PaymentGatewayTypes } from "../../../../../nestJs/modules/payment/dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "../../../../../nestJs/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../../../nestJs/modules/payment/dto/paymentInformationType.dto";
import { TopicNamesType } from "../../../../../nestJs/modules/pubsub/dto/topicName.dto";
import { TxEventType, txEventTypeTxTypeMapping } from "../../../../../nestJs/modules/transaction/dto/txEventType.dto";
import { TxHailingRequestStatus } from "../../../../../nestJs/modules/transaction/dto/txHailingRequest.dto";
import { TxMetadata } from "../../../../../nestJs/modules/transaction/dto/txMetadata.dto";
import { TxTypes } from "../../../../../nestJs/modules/transaction/dto/txType.dto";
import { errorBuilder } from "../../../../../nestJs/modules/utils/utils/error.utils";
import { resetPubsubMocks } from "../../../../utils/@google-cloud/pubsub.specs.utils";
import {
  bindingMock,
  CaptureApiMock,
} from "../../../../utils/cybersource-rest-client/cybersourceRestClientMocks.specs.utils";
import {
  mockCreate,
  mockFindOne,
  mockFindOneBy,
  mockSave,
  mockUpdate,
  mockUpsert,
} from "../../../../utils/services/FakeRepository.specs.utils";

import "../../../../initTests";

describe("meTransaction.controller.ts", () => {
  let controller: MeTransactionController;
  const txId = "2b0e6a42-ebb9-419b-badb-00881dd5b82a";

  beforeEach(() => {
    resetPubsubMocks({ topicNamesType: TopicNamesType.COPY_TRIP_TO_DRIVER_PROCESSING });
    controller = newTestMeTransactionController();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
  });

  describe("addTxEvent", () => {
    describe(TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER, () => {
      it("should return the correct response", async () => {
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() =>
          Promise.resolve({ id: "123", type: TxTypes.HAILING_REQUEST, metadata: {} }),
        ); // Tx
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "456" })); // Merchant
        findOneByIdMock.mockReturnValueOnce(new Promise((resolve) => resolve(new MeterDocument())));

        const result = await controller.addTxEvent(
          "123",
          {
            type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
            content: {
              phoneNumber: "1234567890",
              meter: "DASH123",
            },
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: "123" },
          content: {
            phoneNumber: "1234567890",
            meter: "DASH123",
          },
          type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
        });

        expect(mockSave).toHaveBeenCalledWith({
          id: "123",
          merchant: {
            id: "456",
          },
          txEvents: [
            {
              content: {
                phoneNumber: "1234567890",
                meter: "DASH123",
              },
              createdBy: "789",
              tx: {
                id: "123",
              },
              type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
            },
          ],
          type: TxTypes.HAILING_REQUEST,
          metadata: { status: TxHailingRequestStatus.ACCEPTED },
        });
      });

      it("should return the correct response and set the payoutMerchant when the meter is set to settle to fleet", async () => {
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() =>
          Promise.resolve({ id: "123", type: TxTypes.HAILING_REQUEST, metadata: {} }),
        ); // Tx
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "456" })); // Merchant
        findOneByIdMock.mockImplementationOnce(
          () =>
            new Promise((resolve) => {
              const meterDoc = new MeterDocument();
              meterDoc.settings = { settleToFleet: true, fleetId: "123" };
              resolve(meterDoc);
            }),
        );
        findOneByIdMock.mockImplementationOnce(() => {
          const fleetDoc = new FleetDocument();
          fleetDoc.merchantId = "789";
          return Promise.resolve(fleetDoc);
        });
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "payoutMerchantId" })); // Merchant

        const result = await controller.addTxEvent(
          "123",
          {
            type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
            content: {
              phoneNumber: "1234567890",
              meter: "DASH123",
              fleetMockMeterId: "FLEET123",
            },
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: "123" },
          content: {
            phoneNumber: "1234567890",
            meter: "DASH123",
            fleetMockMeterId: "FLEET123",
          },
          type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
        });

        expect(mockSave).toHaveBeenCalledWith({
          id: "123",
          merchant: {
            id: "456",
          },
          payoutMerchant: {
            id: "payoutMerchantId",
          },
          txEvents: [
            {
              content: {
                phoneNumber: "1234567890",
                meter: "DASH123",
                fleetMockMeterId: "FLEET123",
              },
              createdBy: "789",
              tx: {
                id: "123",
              },
              type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
            },
          ],
          type: TxTypes.HAILING_REQUEST,
          metadata: { status: TxHailingRequestStatus.ACCEPTED },
        });
      });

      it("should throw an error when the tx is not of the right type", async () => {
        mockFindOne.mockReset();
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "123", type: TxTypes.TRIP })); // Tx
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "456" })); // Merchant

        await expect(
          controller.addTxEvent(
            "123",
            {
              type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
              content: {
                phoneNumber: "1234567890",
                meter: "DASH123",
              },
            },
            { user: { uid: "987" } } as Request,
          ),
        ).rejects.toThrow(
          errorBuilder.transaction.eventTypeMismatch(
            "123",
            TxTypes.TRIP,
            txEventTypeTxTypeMapping[TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER],
          ),
        );

        expect(mockSave).not.toHaveBeenCalled();
      });

      it("should save as empty string when the user is not found", async () => {
        mockFindOne.mockReset();
        mockFindOne.mockImplementationOnce(() =>
          Promise.resolve({
            id: "123",
            type: TxTypes.HAILING_REQUEST,
            metadata: { status: TxHailingRequestStatus.PENDING },
          }),
        ); // Tx
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "456" })); // Merchant
        findOneByIdMock.mockReturnValueOnce(new Promise((resolve) => resolve(new MeterDocument())));

        const result = await controller.addTxEvent(
          "123",
          {
            type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
            content: {
              phoneNumber: "1234567890",
              meter: "DASH123",
            },
          },
          {} as Request,
        );
        expect(result.createdBy).toBe("");
        expect(mockSave).toHaveBeenCalled();
      });

      it("should throw an error when the tx is not found", async () => {
        mockFindOne.mockReset();
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() => Promise.resolve(undefined)); // Tx
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "456" })); // Merchant

        await expect(
          controller.addTxEvent(
            "123",
            {
              type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
              content: {
                phoneNumber: "1234567890",
                meter: "DASH123",
              },
            },
            { user: { uid: "987" } } as Request,
          ),
        ).rejects.toThrow(errorBuilder.transaction.notFound("123"));

        expect(mockSave).not.toHaveBeenCalled();
      });
    });

    describe(TxEventType.HAILING_MERCHANT_CANCELS_ORDER, () => {
      it("should return the correct response", async () => {
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() =>
          Promise.resolve({ id: "123", type: TxTypes.HAILING_REQUEST, metadata: { licensePlate: "123" } }),
        ); // Tx

        const result = await controller.addTxEvent(
          "123",
          {
            type: TxEventType.HAILING_MERCHANT_CANCELS_ORDER,
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: "123" },
          type: TxEventType.HAILING_MERCHANT_CANCELS_ORDER,
        });

        expect(mockSave).toHaveBeenCalledWith({
          id: "123",
          merchant: null,
          payoutMerchant: null,
          metadata: { licensePlate: undefined, status: TxHailingRequestStatus.PENDING },
          txEvents: [
            {
              createdBy: "789",
              tx: {
                id: "123",
              },
              type: TxEventType.HAILING_MERCHANT_CANCELS_ORDER,
            },
          ],
          type: TxTypes.HAILING_REQUEST,
        });
      });
    });

    describe(TxEventType.HAILING_USER_CREATES_ORDER, () => {
      it("should return the correct response", async () => {
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() =>
          Promise.resolve({
            id: "123",
            type: TxTypes.HAILING_REQUEST,
            metadata: { status: TxHailingRequestStatus.PENDING },
          }),
        ); // Tx

        const result = await controller.addTxEvent(
          "123",
          {
            type: TxEventType.HAILING_USER_CREATES_ORDER,
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: "123" },
          type: TxEventType.HAILING_USER_CREATES_ORDER,
        });

        expect(mockSave).toHaveBeenCalledWith({
          id: "123",
          txEvents: [
            {
              createdBy: "789",
              tx: {
                id: "123",
              },
              type: TxEventType.HAILING_USER_CREATES_ORDER,
            },
          ],
          type: TxTypes.HAILING_REQUEST,
          metadata: {
            status: "PENDING",
          },
        });
      });
    });

    describe(TxEventType.HAILING_USER_CANCELS_ORDER, () => {
      const createTx = () => {
        const tx = new Tx();
        tx.id = "123";
        tx.type = TxTypes.HAILING_REQUEST;
        tx.metadata = {} as TxMetadata;
        tx.txApp = new TxApp();
        tx.txApp.id = "123";
        tx.txApp.name = TxAppsNames.TAPXI;
        const paymentTx = new PaymentTx();
        paymentTx.type = PaymentInformationType.AUTH;
        paymentTx.status = PaymentInformationStatus.SUCCESS;
        paymentTx.gateway = PaymentGatewayTypes.GLOBAL_PAYMENTS;
        paymentTx.createdAt = new Date();
        paymentTx.id = "123";
        tx.paymentTx = [paymentTx];
        return tx;
      };

      it("should return the correct response", async () => {
        const tx = createTx();
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() => Promise.resolve(tx)); // Tx
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ ...tx.paymentTx?.[0], tx })); // PaymentTx
        mockCreate.mockImplementationOnce(() =>
          Promise.resolve({
            ...tx.paymentTx?.[0],
            tx,
            type: PaymentInformationType.VOID,
            status: PaymentInformationStatus.SUCCESS,
          }),
        ); // PaymentTx voided

        const result = await controller.addTxEvent(
          "123",
          {
            type: TxEventType.HAILING_USER_CANCELS_ORDER,
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: "123" },
          type: TxEventType.HAILING_USER_CANCELS_ORDER,
          content: {
            charges: {
              cancellationFee: 0,
            },
          },
        });

        expect(mockSave).toHaveBeenCalledWith({
          id: "123",
          txApp: expect.any(TxApp),
          paymentTx: expect.any(Array),
          metadata: { cancelledAt: expect.any(Date), status: TxHailingRequestStatus.CANCELLED },
          txEvents: [
            {
              createdBy: "789",
              tx: {
                id: "123",
              },
              type: TxEventType.HAILING_USER_CANCELS_ORDER,
              content: {
                charges: {
                  cancellationFee: 0,
                },
              },
            },
          ],
          type: TxTypes.HAILING_REQUEST,
        });
      });

      it("should return the correct response for a scheduled ride", async () => {
        const tx = createTx();
        tx.metadata = { request: { time: new Date(Date.now() + 60 * 60 * 1000) } } as TxMetadata;
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() => Promise.resolve(tx)); // Tx
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ ...tx.paymentTx?.[0], tx })); // PaymentTx
        mockCreate.mockImplementationOnce(() =>
          Promise.resolve({
            ...tx.paymentTx?.[0],
            tx,
            type: PaymentInformationType.VOID,
            status: PaymentInformationStatus.SUCCESS,
          }),
        ); // PaymentTx voided

        const result = await controller.addTxEvent(
          "123",
          {
            type: TxEventType.HAILING_USER_CANCELS_ORDER,
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: "123" },
          type: TxEventType.HAILING_USER_CANCELS_ORDER,
          content: {
            charges: {
              cancellationFee: 0,
            },
          },
        });

        expect(mockSave).toHaveBeenCalledWith({
          id: "123",
          txApp: expect.any(TxApp),
          paymentTx: expect.any(Array),
          metadata: {
            cancelledAt: expect.any(Date),
            status: TxHailingRequestStatus.CANCELLED,
            request: { time: expect.any(Date) },
          },
          txEvents: [
            {
              createdBy: "789",
              tx: {
                id: "123",
              },
              type: TxEventType.HAILING_USER_CANCELS_ORDER,
              content: {
                charges: {
                  cancellationFee: 0,
                },
              },
            },
          ],
          type: TxTypes.HAILING_REQUEST,
        });
      });

      it("should throw an error when cancelled below the threshold of 3 minutes and there is no auth", async () => {
        const tx = new Tx();
        tx.id = "123e4567-e89b-12d3-a456-************";
        tx.type = TxTypes.HAILING_REQUEST;
        tx.metadata = {} as TxMetadata;
        const txEvent = new TxEvent();
        txEvent.type = TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER;
        txEvent.createdAt = new Date(Date.now() - 5 * 60 * 1000);
        tx.txEvents = [txEvent];
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() => Promise.resolve(tx)); // Tx

        await expect(
          controller.addTxEvent(
            "123e4567-e89b-12d3-a456-************",
            {
              type: TxEventType.HAILING_USER_CANCELS_ORDER,
            },
            { user: { uid: "987" } } as Request,
          ),
        ).rejects.toThrow(errorBuilder.transaction.successAuthNotFound(tx.id));

        expect(mockSave).not.toHaveBeenCalled();
      });

      it("should return the correct response and capture a cancellation fee when cancelled below the threshold of 3 minutes", async () => {
        const tx = new Tx();
        tx.id = txId;
        tx.type = TxTypes.HAILING_REQUEST;
        tx.metadata = {} as TxMetadata;
        const txEvent = new TxEvent();
        txEvent.type = TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER;
        txEvent.createdAt = new Date();
        txEvent.createdAt.setMinutes(txEvent.createdAt.getMinutes() - 5);
        tx.txEvents = [txEvent];
        const paymentTx = new PaymentTx();
        paymentTx.type = PaymentInformationType.AUTH;
        paymentTx.status = PaymentInformationStatus.SUCCESS;
        paymentTx.gateway = PaymentGatewayTypes.GLOBAL_PAYMENTS;
        paymentTx.tx = tx;
        tx.paymentTx = [paymentTx];
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() => Promise.resolve(tx)); // Tx
        getHailConfigMock.mockResolvedValueOnce({
          fleetCancellationFee: 20,
          dashCancellationFee: 15,
        });

        const result = await controller.addTxEvent(
          txId,
          {
            type: TxEventType.HAILING_USER_CANCELS_ORDER,
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: txId },
          type: TxEventType.HAILING_USER_CANCELS_ORDER,
          content: {
            charges: {
              cancellationFee: 20,
              cancellationFeeBreakdown: {
                total: 20,
                driverPayout: 15,
                dashFee: 5,
              },
            },
          },
        });

        expect(mockSave).toHaveBeenCalledTimes(3);
      });

      it("should return the correct response and capture a cancellation fee when cancelled below the threshold of 60 minutes for scheduled ride", async () => {
        const tx = new Tx();
        tx.id = txId;
        tx.type = TxTypes.HAILING_REQUEST;
        tx.metadata = { request: { time: new Date(Date.now() + 40 * 60 * 1000) } } as TxMetadata;
        const txEvent = new TxEvent();
        txEvent.type = TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER;
        txEvent.createdAt = dayjs().subtract(5, "minutes").toDate();
        tx.txEvents = [txEvent];
        const paymentTx = new PaymentTx();
        paymentTx.type = PaymentInformationType.AUTH;
        paymentTx.status = PaymentInformationStatus.SUCCESS;
        paymentTx.gateway = PaymentGatewayTypes.GLOBAL_PAYMENTS;
        paymentTx.tx = tx;
        tx.paymentTx = [paymentTx];
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() => Promise.resolve(tx)); // Tx
        getHailConfigMock.mockResolvedValueOnce({
          fleetCancellationFee: 110,
          dashCancellationFee: 15,
        });

        const result = await controller.addTxEvent(
          txId,
          {
            type: TxEventType.HAILING_USER_CANCELS_ORDER,
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: txId },
          type: TxEventType.HAILING_USER_CANCELS_ORDER,
          content: {
            charges: {
              cancellationFee: 20,
              cancellationFeeBreakdown: {
                total: 20,
                driverPayout: 15,
                dashFee: 5,
              },
            },
          },
        });

        expect(mockSave).toHaveBeenCalledTimes(3);
      });

      it("should return the correct response and capture a cancellation fee when cancelled below the threshold of 60 minutes for scheduled ride for fleet", async () => {
        const tx = new Tx();
        tx.id = txId;
        tx.type = TxTypes.HAILING_REQUEST;
        tx.metadata = {
          request: { time: new Date(Date.now() + 40 * 60 * 1000), platformType: PlatformType.FLEET },
        } as TxMetadata;
        const txEvent = new TxEvent();
        txEvent.type = TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER;
        txEvent.createdAt = dayjs().subtract(5, "minutes").toDate();
        tx.txEvents = [txEvent];
        const paymentTx = new PaymentTx();
        paymentTx.type = PaymentInformationType.AUTH;
        paymentTx.status = PaymentInformationStatus.SUCCESS;
        paymentTx.gateway = PaymentGatewayTypes.GLOBAL_PAYMENTS;
        paymentTx.tx = tx;
        paymentTx.amount = 120;
        tx.paymentTx = [paymentTx];
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() => Promise.resolve(tx)); // Tx
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "123", txId: tx.id })); // Tx
        mockExecute.mockImplementationOnce(() => Promise.resolve({ bookingFee: 120, cancellationFee: 80 }));

        getHailConfigMock.mockResolvedValueOnce({
          fleetCancellationFee: 120,
          dashCancellationFee: 15,
        });

        const result = await controller.addTxEvent(
          txId,
          {
            type: TxEventType.HAILING_USER_CANCELS_ORDER,
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: txId },
          type: TxEventType.HAILING_USER_CANCELS_ORDER,
          content: {
            charges: {
              cancellationFee: 120,
              cancellationFeeBreakdown: {
                total: 120,
                driverPayout: 80,
                dashFee: 0,
              },
            },
          },
        });

        expect(mockSave).toHaveBeenCalledTimes(3);
      });

      it("should return the correct response and capture a cancellation fee when cancelled below the threshold of 120 minutes for scheduled ride for fleet", async () => {
        const tx = new Tx();
        tx.id = txId;
        tx.type = TxTypes.HAILING_REQUEST;
        tx.metadata = {
          request: { time: new Date(Date.now() + 120 * 60 * 1000), platformType: PlatformType.FLEET },
        } as TxMetadata;
        const txEvent = new TxEvent();
        txEvent.type = TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER;
        txEvent.createdAt = dayjs().subtract(5, "minutes").toDate();
        tx.txEvents = [txEvent];
        const paymentTx = new PaymentTx();
        paymentTx.type = PaymentInformationType.AUTH;
        paymentTx.status = PaymentInformationStatus.SUCCESS;
        paymentTx.gateway = PaymentGatewayTypes.GLOBAL_PAYMENTS;
        paymentTx.tx = tx;
        paymentTx.amount = 120;
        tx.paymentTx = [paymentTx];
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() => Promise.resolve(tx)); // Tx
        getHailConfigMock.mockResolvedValueOnce({
          fleetCancellationFee: 120,
          dashCancellationFee: 15,
        });
        mockExecute.mockImplementationOnce(() => Promise.resolve({ bookingFee: 120, cancellationFee: 10 }));

        const result = await controller.addTxEvent(
          txId,
          {
            type: TxEventType.HAILING_USER_CANCELS_ORDER,
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: txId },
          type: TxEventType.HAILING_USER_CANCELS_ORDER,
          content: {
            charges: {
              cancellationFee: 20,
              cancellationFeeBreakdown: {
                total: 20,
                driverPayout: 10,
                dashFee: 0,
              },
            },
          },
        });

        expect(mockSave).toHaveBeenCalledTimes(3);
      });

      it("should return the correct response when fleet order is cancelled within 3 mins", async () => {
        const tx = new Tx();
        tx.id = txId;
        tx.type = TxTypes.HAILING_REQUEST;
        tx.metadata = {
          request: { time: new Date(Date.now() + 120 * 60 * 1000), platformType: PlatformType.FLEET },
        } as TxMetadata;
        const txEvent = new TxEvent();
        txEvent.type = TxEventType.HAILING_USER_CANCELS_ORDER;
        txEvent.createdAt = dayjs().subtract(1, "minutes").toDate();
        tx.txEvents = [txEvent];
        const paymentTx = new PaymentTx();
        paymentTx.id = "123";
        paymentTx.type = PaymentInformationType.AUTH;
        paymentTx.status = PaymentInformationStatus.SUCCESS;
        paymentTx.gateway = PaymentGatewayTypes.GLOBAL_PAYMENTS;
        paymentTx.tx = tx;
        paymentTx.amount = 120;
        tx.paymentTx = [paymentTx];
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() => Promise.resolve(tx)); // Tx
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ ...paymentTx, tx })); // PaymentTx
        getHailConfigMock.mockResolvedValueOnce({
          fleetCancellationFee: 115,
          dashCancellationFee: 15,
        });

        const result = await controller.addTxEvent(
          txId,
          {
            type: TxEventType.HAILING_USER_CANCELS_ORDER,
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: txId },
          type: TxEventType.HAILING_USER_CANCELS_ORDER,
          content: {
            charges: {
              cancellationFee: 0,
            },
          },
        });

        expect(mockSave).toHaveBeenCalledTimes(5);
      });

      it("should throw an error when cancelled below the threshold of 3 minutes and capture fails", async () => {
        CaptureApiMock.mockImplementation(() => {
          return {
            capturePayment: bindingMock((...args: any[]) =>
              /// get last arg
              args[args.length - 1](null, {
                id: "123",
                state: "ACTIVE",
                status: "FAILED",
              }),
            ),
          };
        });

        const tx = new Tx();
        tx.id = "123e4567-e89b-12d3-a456-************";
        tx.type = TxTypes.HAILING_REQUEST;
        tx.metadata = {} as TxMetadata;
        const txEvent = new TxEvent();
        txEvent.type = TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER;
        txEvent.createdAt = new Date();
        txEvent.createdAt.setMinutes(txEvent.createdAt.getMinutes() - 5);
        tx.txEvents = [txEvent];
        const paymentTx = new PaymentTx();
        paymentTx.type = PaymentInformationType.AUTH;
        paymentTx.status = PaymentInformationStatus.SUCCESS;
        paymentTx.gateway = PaymentGatewayTypes.GLOBAL_PAYMENTS;
        paymentTx.tx = tx;
        tx.paymentTx = [paymentTx];
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() => Promise.resolve(tx)); // Tx
        saveMock.mockImplementation(() => tx);
        getHailConfigMock.mockResolvedValueOnce({
          fleetCancellationFee: 20,
          dashCancellationFee: 15,
        });

        const result = await controller.addTxEvent(
          "123e4567-e89b-12d3-a456-************",
          {
            type: TxEventType.HAILING_USER_CANCELS_ORDER,
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result.createdBy).toBe("789");
        expect(mockSave).toHaveBeenCalledTimes(3);
      });
    });

    describe(TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED, () => {
      it("should return the correct response", async () => {
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() =>
          Promise.resolve({
            id: "123",
            type: TxTypes.HAILING_REQUEST,
            txApp: new TxApp(),
            merchant: new Merchant(),
            paymentTx: [
              { id: "123", tx: { id: "123" } },
              { id: "456", tx: { id: "123" } },
              { id: "789", tx: { id: "123" } },
            ],
            metadata: {},
          }),
        ); // Tx

        mockFindOneBy.mockResolvedValueOnce(null); // Trip Tx
        const fakeTrip = { id: "123987", licensePlate: "mm-123" } as unknown as TripDocument;
        findOneByIdMock.mockReturnValueOnce(new Promise((resolve) => resolve(fakeTrip)));

        const result = await controller.addTxEvent(
          "123",
          {
            type: TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED,
            content: { txId: "123987", meterId: "mm-123" },
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: "123" },
          content: {
            txId: "123987",
            meterId: "mm-123",
          },
          type: TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED,
        });

        expect(mockSave).toHaveBeenCalledWith({
          id: "123",
          parentTx: {
            id: "123987",
          },
          merchant: expect.any(Merchant),
          txApp: expect.any(TxApp),
          paymentTx: [],
          txEvents: [
            {
              createdBy: "789",
              tx: {
                id: "123",
              },
              content: {
                txId: "123987",
                meterId: "mm-123",
              },
              type: TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED,
            },
          ],
          type: TxTypes.HAILING_REQUEST,
          metadata: { status: TxHailingRequestStatus.ON_GOING },
        });

        expect(mockUpsert).toHaveBeenCalledWith(
          {
            id: "123987",
            txApp: {},
            type: TxTypes.TRIP,
            merchant: {
              payoutPeriod: "DAILY",
            },
            metadata: fakeTrip,
          },
          { conflictPaths: { id: true } },
        );

        expect(mockUpdate).toHaveBeenCalledTimes(3);
        expect(mockUpdate).toHaveBeenNthCalledWith(1, "123", { id: "123", tx: { id: "123987" } });
        expect(mockUpdate).toHaveBeenNthCalledWith(2, "456", { id: "456", tx: { id: "123987" } });
        expect(mockUpdate).toHaveBeenNthCalledWith(3, "789", { id: "789", tx: { id: "123987" } });
      });
    });

    describe(TxEventType.HAILING_MERCHANT_APPROACHING_DESTINATION, () => {
      it("should return the correct response", async () => {
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() =>
          Promise.resolve({
            id: "123",
            type: TxTypes.HAILING_REQUEST,
            metadata: {},
          }),
        ); // Tx

        const result = await controller.addTxEvent(
          "123",
          {
            type: TxEventType.HAILING_MERCHANT_APPROACHING_DESTINATION,
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: "123" },
          type: TxEventType.HAILING_MERCHANT_APPROACHING_DESTINATION,
        });

        expect(mockSave).toHaveBeenCalledWith({
          id: "123",
          txEvents: [
            {
              createdBy: "789",
              tx: {
                id: "123",
              },
              type: TxEventType.HAILING_MERCHANT_APPROACHING_DESTINATION,
            },
          ],
          type: TxTypes.HAILING_REQUEST,
          metadata: { status: TxHailingRequestStatus.APPROACHING },
        });
      });
    });

    describe(TxEventType.HAILING_ORDER_COMPLETED, () => {
      it("should return the correct response", async () => {
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() =>
          Promise.resolve({
            id: "123",
            type: TxTypes.HAILING_REQUEST,
            metadata: {},
          }),
        ); // Tx

        const result = await controller.addTxEvent(
          "123",
          {
            type: TxEventType.HAILING_ORDER_COMPLETED,
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: "123" },
          type: TxEventType.HAILING_ORDER_COMPLETED,
        });

        expect(mockSave).toHaveBeenCalledWith({
          id: "123",
          txEvents: [
            {
              createdBy: "789",
              tx: {
                id: "123",
              },
              type: TxEventType.HAILING_ORDER_COMPLETED,
            },
          ],
          type: TxTypes.HAILING_REQUEST,
          metadata: { status: TxHailingRequestStatus.COMPLETED, completedAt: expect.any(Date) },
        });
      });
    });

    describe(TxEventType.HAILING_MERCHANT_ARRIVED_DESTINATION, () => {
      it("should return the correct response", async () => {
        mockFindOne.mockImplementationOnce(() => Promise.resolve({ id: "789" })); // User
        mockFindOne.mockImplementationOnce(() =>
          Promise.resolve({
            id: "123",
            type: TxTypes.HAILING_REQUEST,
            metadata: {},
          }),
        ); // Tx

        const result = await controller.addTxEvent(
          "123",
          {
            type: TxEventType.HAILING_MERCHANT_ARRIVED_DESTINATION,
          },
          { user: { uid: "987" } } as Request,
        );

        expect(result).toEqual({
          createdBy: "789",
          tx: { id: "123" },
          type: TxEventType.HAILING_MERCHANT_ARRIVED_DESTINATION,
        });

        expect(mockSave).toHaveBeenCalledWith({
          id: "123",
          txEvents: [
            {
              createdBy: "789",
              tx: {
                id: "123",
              },
              type: TxEventType.HAILING_MERCHANT_ARRIVED_DESTINATION,
            },
          ],
          type: TxTypes.HAILING_REQUEST,
          metadata: { status: TxHailingRequestStatus.ARRIVED },
        });
      });
    });
  });
});
