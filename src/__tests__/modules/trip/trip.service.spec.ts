import { HttpService } from "@nestjs/axios";
import { ConfigService } from "@nestjs/config";
import { Cache } from "cache-manager";
import { Timestamp } from "firebase-admin/firestore";

import { PassengerInformation } from "@legacy/model/trip";
import Payout from "@nest/modules/database/entities/payout.entity";
import { PaymentType } from "@nest/modules/payment/dto/paymentType.dto";
import { expectedPublishWhatsappMessage } from "@tests/utils/fakeData/fakeMessage.specs.utils";

import { AppDatabaseService } from "../../../nestJs/modules/appDatabase/appDatabase.service";
import {
  NotificationDocument,
  NotificationDocumentType,
  NotificationStatus,
} from "../../../nestJs/modules/appDatabase/documents/driver.document";
import { DriverTripDocument } from "../../../nestJs/modules/appDatabase/documents/driverTrip.document";
import { MeterDocument } from "../../../nestJs/modules/appDatabase/documents/meter.document";
import { Billing, TripDocument } from "../../../nestJs/modules/appDatabase/documents/trip.document";
import { TxAppsNames } from "../../../nestJs/modules/apps/dto/Apps.dto";
import { BankResponseLineItemStatus, PayoutBankFileRow } from "../../../nestJs/modules/bank/dto/payoutBankFile.dto";
import TxApp from "../../../nestJs/modules/database/entities/app.entity";
import Merchant from "../../../nestJs/modules/database/entities/merchant.entity";
import PaymentTx from "../../../nestJs/modules/database/entities/paymentTx.entity";
import Tx from "../../../nestJs/modules/database/entities/tx.entity";
import { TxAppRepository } from "../../../nestJs/modules/database/repositories/app.repository";
import { DiscountRepository } from "../../../nestJs/modules/database/repositories/discount.repository";
import { MerchantRepository } from "../../../nestJs/modules/database/repositories/merchant.repository";
import { MerchantNotificationTokenRepository } from "../../../nestJs/modules/database/repositories/merchantNotificationToken.repository";
import { PaymentInstrumentRepository } from "../../../nestJs/modules/database/repositories/paymentInstument.repository";
import { PaymentTxRepository } from "../../../nestJs/modules/database/repositories/paymentTx.repository";
import { TxRepository } from "../../../nestJs/modules/database/repositories/tx.repository";
import { TxTagRepository } from "../../../nestJs/modules/database/repositories/txTag.repository";
import { UserRepository } from "../../../nestJs/modules/database/repositories/user.repository";
import { UserNotificationTokenRepository } from "../../../nestJs/modules/database/repositories/userNotificationToken.repository";
import { FcmService } from "../../../nestJs/modules/fcm/fcm.service";
import {
  NotificationType,
  notificationTemplate,
} from "../../../nestJs/modules/message/messageFactory/modules/notification/notification.dto";
import { PaymentInformationStatus } from "../../../nestJs/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentService } from "../../../nestJs/modules/payment/payment.service";
import { ManualService } from "../../../nestJs/modules/payment/paymentFactory/modules/manual/manual.service";
import { SoepayService } from "../../../nestJs/modules/payment/paymentFactory/modules/soepay/soepay.service";
import { PaymentFactoryService } from "../../../nestJs/modules/payment/paymentFactory/paymentFactory.service";
import { PubSubService } from "../../../nestJs/modules/pubsub/pubsub.service";
import {
  PaymentCardInformation,
  ReceiptLanguageType,
  TxReceiptTrip,
} from "../../../nestJs/modules/transaction/dto/txReceipt.dto";
import { TxTypes } from "../../../nestJs/modules/transaction/dto/txType.dto";
import { TransactionService } from "../../../nestJs/modules/transaction/transaction.service";
import { TripStatus } from "../../../nestJs/modules/transaction/transactionFactory/modules/trip/dto/tripStatus.dto";
import { TripService } from "../../../nestJs/modules/transaction/transactionFactory/modules/trip/trip.service";
import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";
import { UtilsService } from "../../../nestJs/modules/utils/utils.service";
import { LanguageOption } from "../../../nestJs/modules/validation/dto/language.dto";
import expects from "../../utils/expects.specs.utils";
import {
  generateFakePaymentTx,
  generateFakePaymentTxAlipaySale,
  generateFakePaymentTxAuthAndCapture,
  generateFakePaymentTxSale,
} from "../../utils/fakeData/fakeDataPaymentTx.specs.utils";
import {
  soepayAuthSuccess,
  soepayCaptureFailure,
  soepaySaleSuccess,
} from "../../utils/fakeData/fakeDataSoepay.specs.utils";
import { fakeTrip, fakeTripData } from "../../utils/fakeData/fakeTripData.specs.utils";
import FakeLoggerService from "../../utils/fakeLogger.service.specs";
import MockAppDatabaseService, {
  addMock,
  getSessionMock,
  removeDriverAndSessionMock,
  updateSessionTripWithObjectMock,
  updateDriverTripWithObjectMock,
  updateMeterTripMock,
  getActiveLockMock,
  setMock,
  findOneByIdMock,
} from "../../utils/services/FakeAppDatabaseService.specs.utils";
import FakeCache, { fakeCacheMockGet } from "../../utils/services/FakeCache.specs.utils";
import FakeConfigService from "../../utils/services/FakeConfigService.specs.utils";
import FakePubSubService, {
  mockPublishMessageForMessageProcessingParams,
  mockPublishMessageForTripProcessing,
} from "../../utils/services/FakePubSubService.specs.utils";
import FakeRepository, { mockFindOne, mockSave } from "../../utils/services/FakeRepository.specs.utils";
import {
  newTestAppDatabaseService,
  newTestCampaignService,
  newTestGlobalPaymentPaymentService,
  newTestJsonLogicService,
  newTestKrakenService,
  newTestLocationService,
  newTestLoggerServiceAdapter,
  newTestTransactionService,
} from "../../utils/services/TestServices.specs.utils";

jest.mock("moment-timezone", () => {
  const momentMock = jest.fn(() => ({
    tz: jest.fn().mockReturnThis(),
    format: jest.fn().mockReturnValue("2023-01-01 00:01:00"),
  }));

  return momentMock;
});

jest.mock("moment", () => {
  const momentMock = jest.fn(() => ({
    tz: jest.fn().mockReturnThis(),
    format: jest.fn().mockReturnValue("2023-01-01 00:01:00"),
  }));

  return momentMock;
});

describe("trip.service", () => {
  let service: TripService;
  const utilsService = new UtilsService();

  beforeEach(async () => {
    service = new TripService(
      new FakePubSubService() as unknown as PubSubService,
      new PaymentService(
        new PaymentFactoryService(
          new SoepayService(
            new ConfigService(),
            new HttpService(),
            new FakeRepository() as unknown as PaymentTxRepository,
            FakeLoggerService,
          ),
          new ManualService(),
          newTestGlobalPaymentPaymentService(),
          newTestKrakenService(),
        ),
        new FakeRepository() as unknown as PaymentTxRepository,
        new FakeRepository() as unknown as TxTagRepository,
        new FakeRepository() as unknown as PaymentInstrumentRepository,
        new FakeRepository() as unknown as TxRepository,
        newTestLoggerServiceAdapter(),
        newTestAppDatabaseService(),
      ),
      new FakeConfigService() as unknown as ConfigService,
      utilsService,
      new MockAppDatabaseService() as unknown as AppDatabaseService,
      new FakeRepository() as unknown as TxRepository,
      new FakeRepository() as unknown as TxAppRepository,
      new FakeRepository() as unknown as DiscountRepository,
      FakeLoggerService,
      new FakeCache() as unknown as Cache,
      new FcmService(
        FakeLoggerService,
        new MockAppDatabaseService() as unknown as AppDatabaseService,
        new FakeRepository() as unknown as UserRepository,
        new FakeRepository() as unknown as UserNotificationTokenRepository,
        new FakeRepository() as unknown as MerchantRepository,
        new FakeRepository() as unknown as MerchantNotificationTokenRepository,
      ),
      new FakeRepository() as unknown as UserRepository,
      new FakeRepository() as unknown as UserNotificationTokenRepository,
      newTestLocationService(),
      newTestCampaignService(),
      newTestJsonLogicService(),
      newTestTransactionService(),
    );
    mockPublishMessageForTripProcessing.mockImplementation(() => {
      return () => new Promise((resolve) => resolve(""));
    });
    fakeCacheMockGet.mockImplementationOnce(() => TxApp.fromJson({ id: "1234", name: TxAppsNames.TAPXI }));
    mockFindOne.mockImplementation(() => TxApp.fromJson({ id: "1234", name: TxAppsNames.TAPXI }));
  });

  describe("meterTripChange", () => {
    it("should call the pubsub service with the right payload", async () => {
      await service.meterTripChange("meterId", "tripId", {
        ...fakeTripData,
        payment_information: [
          {
            creation_time: new Timestamp(1, 1),
            type: "AUTH",
            body: JSON.stringify(soepayAuthSuccess),
            status: "SUCCESS",
          },
        ],
      });
      expect(mockPublishMessageForTripProcessing).toHaveBeenCalledWith({
        tx: {
          txApp: { id: "1234", name: TxAppsNames.TAPXI },
          type: "TRIP",
          id: "tripId",
          data: {
            ...utilsService.case.camelizeKeys(fakeTripData),
            creationTime: fakeTripData.creation_time.toDate(),
            lastUpdateTime: fakeTripData.last_update_time.toDate(),
            tripEnd: fakeTripData.trip_end.toDate(),
            tripStart: fakeTripData.trip_start.toDate(),
            isPaymentProcessing: true,
          },
        },
        merchant: { name: "Taxi D", phoneNumber: "+1231244" },
        paymentTx: [
          {
            id: "9081f05c-4ffd-49a8-980f-ad4b6f852989",
            amount: 23.5,
            gatewayTransactionId: "4b47b9d8820a0b4400440a46e49a3c4d7ad8e9c0",
            cardNumber: "436605******8868",
            paymentMethod: "VISA",
            status: "SUCCESS",
            type: "AUTH",
            gateway: "SOEPAY",
            gatewayResponse: soepayAuthSuccess,
            createdAt: new Date("2023-05-22T22:49:12.000Z"),
          },
        ],
        txProcessed: true,
        doVoid: true,
        isDirectSale: false,
        isAfterTxEnd: false,
        isCaptureInApp: false,
        isTripEnd: true,
      });
    });

    it("should call the pubsub service with the right payload, and if it has user_id, should copy trip to user/trip", async () => {
      const pushNotificationToUserWhenTripEndSpy = jest
        .spyOn(service, "pushNotificationToUserWhenTripEnd")
        .mockResolvedValue();
      await service.meterTripChange("meterId", "tripId", {
        ...fakeTripData,
        payment_information: [
          {
            creation_time: new Timestamp(1, 1),
            type: "AUTH",
            body: JSON.stringify(soepayAuthSuccess),
            status: "SUCCESS",
          },
        ],
        user: {
          id: "A1B2C3D4E5F6G7H8",
        },
      });
      expect(mockPublishMessageForTripProcessing).toHaveBeenCalledWith({
        tx: {
          txApp: { id: "1234", name: TxAppsNames.TAPXI },
          type: "TRIP",
          id: "tripId",
          data: {
            ...utilsService.case.camelizeKeys(fakeTripData),
            creationTime: fakeTripData.creation_time.toDate(),
            lastUpdateTime: fakeTripData.last_update_time.toDate(),
            tripEnd: fakeTripData.trip_end.toDate(),
            tripStart: fakeTripData.trip_start.toDate(),
            isPaymentProcessing: true,
          },
        },
        merchant: { name: "Taxi D", phoneNumber: "+1231244" },
        paymentTx: [
          {
            id: "9081f05c-4ffd-49a8-980f-ad4b6f852989",
            amount: 23.5,
            gatewayTransactionId: "4b47b9d8820a0b4400440a46e49a3c4d7ad8e9c0",
            cardNumber: "436605******8868",
            paymentMethod: "VISA",
            status: "SUCCESS",
            type: "AUTH",
            gateway: "SOEPAY",
            gatewayResponse: soepayAuthSuccess,
            createdAt: new Date("2023-05-22T22:49:12.000Z"),
          },
        ],
        txProcessed: true,
        doVoid: true,
        isDirectSale: false,
        isAfterTxEnd: false,
        isCaptureInApp: false,
        isTripEnd: true,
      });
      expect(pushNotificationToUserWhenTripEndSpy).toHaveBeenCalledWith("tripId", 23.5, "A1B2C3D4E5F6G7H8");
    });

    it("call the pubsub service with the right payload, when have a success sale payment, should also doVoid", async () => {
      await service.meterTripChange("meterId", "tripId", {
        ...fakeTripData,
        payment_information: [
          {
            creation_time: new Timestamp(1, 1),
            type: "SALE",
            body: JSON.stringify(soepaySaleSuccess),
            status: "SUCCESS",
          },
        ],
      });
      expect(mockPublishMessageForTripProcessing).toHaveBeenCalledWith({
        tx: {
          txApp: { id: "1234", name: TxAppsNames.TAPXI },
          type: "TRIP",
          id: "tripId",
          data: {
            ...utilsService.case.camelizeKeys(fakeTripData),
            creationTime: fakeTripData.creation_time.toDate(),
            lastUpdateTime: fakeTripData.last_update_time.toDate(),
            tripEnd: fakeTripData.trip_end.toDate(),
            tripStart: fakeTripData.trip_start.toDate(),
            isPaymentProcessing: true,
          },
        },
        merchant: { name: "Taxi D", phoneNumber: "+1231244" },
        paymentTx: [
          {
            id: "9081f05c-4ffd-49a8-980f-ad4b6f852989",
            amount: 23.5,
            gatewayTransactionId: "4b47b9d8820a0b4400440a46e49a3c4d7ad8e9c0",
            cardNumber: "436605******8868",
            paymentMethod: "VISA",
            status: "SUCCESS",
            type: "SALE",
            gateway: "SOEPAY",
            gatewayResponse: soepaySaleSuccess,
            createdAt: new Date("2023-05-22T22:49:12.000Z"),
          },
        ],
        txProcessed: true,
        doVoid: true,
        isDirectSale: false,
        isAfterTxEnd: false,
        isCaptureInApp: false,
        isTripEnd: true,
      });
    });

    it("should call the pubsub service with the right payload when the transaction status is ERROR", async () => {
      await service.meterTripChange("meterId", "tripId", {
        ...fakeTripData,
        payment_information: [
          {
            creation_time: new Timestamp(1, 1),
            type: "CAPTURE",
            body: JSON.stringify(soepayCaptureFailure),
            status: "FAILURE",
          },
        ],
      });
      expect(mockPublishMessageForTripProcessing).toHaveBeenCalledWith({
        tx: {
          txApp: { id: "1234", name: TxAppsNames.TAPXI },
          type: TransactionService.TX_TYPES.TRIP,
          id: "tripId",
          data: expect.objectContaining({
            fare: 22,
            language: "en",
            tripTotal: 22,
            waitTime: 0,
            dashFeeConstant: 1.5,
            dashFee: 12,
          }),
        },
        merchant: { name: "Taxi D", phoneNumber: "+1231244" },
        paymentTx: [
          expect.objectContaining({
            id: expect.stringMatching(expects.uuid),
            status: "FAILURE",
            type: "CAPTURE",
            gateway: "SOEPAY",
          }),
        ],
        txProcessed: true,
        doVoid: false,
        isDirectSale: false,
        isAfterTxEnd: false,
        isCaptureInApp: false,
        isTripEnd: true,
      });
    });
  });

  describe("meterTripCreate", () => {
    beforeEach(() => {
      removeDriverAndSessionMock.mockClear();
    });

    it("should call the removeDriverAndSession method", async () => {
      const date = new Date();
      date.setDate(date.getDate() - 1);
      getSessionMock.mockResolvedValueOnce({ endTime: date });
      findOneByIdMock.mockReturnValueOnce(new Promise((resolve) => resolve(new MeterDocument())));
      await service.meterTripCreate("DASH02T", "12345678", {
        session: { id: "PEnBqkZtj0M5FNjDjiE6" },
        trip_start: Timestamp.fromDate(new Date()),
        billing: {},
      });

      expect(removeDriverAndSessionMock).toBeCalled();
    });

    it("should not call the removeDriverAndSession method when session's end_time is later than trip' trip_start", async () => {
      const date = new Date();
      date.setDate(date.getDate() + 1);
      getSessionMock.mockResolvedValueOnce({ endTime: date });
      findOneByIdMock.mockReturnValueOnce(new Promise((resolve) => resolve(new MeterDocument())));
      await service.meterTripCreate("DASH02T", "12345678", {
        session: { id: "PEnBqkZtj0M5FNjDjiE6" },
        trip_start: Timestamp.fromDate(new Date()),
        billing: {},
      });

      expect(removeDriverAndSessionMock).not.toBeCalled();
    });
  });

  describe("postPaymentProcess", () => {
    it("should throw an error when the tx is not a txTrip", async () => {
      const tx = new Tx();
      tx.type = TxTypes.TX_ADJUSTMENT;

      expect(service.postPaymentProcess(tx, true)).rejects.toThrowError(
        errorBuilder.transaction.wrongImplement(tx.type, "TripService/postPaymentProcess"),
      );
    });
  });

  describe("postCashPaymentProcess", () => {
    it("should throw an error when the tx is not a txTrip", async () => {
      const tx = new Tx();
      tx.type = TxTypes.TX_ADJUSTMENT;

      expect(service.postCashPaymentProcess(tx)).rejects.toThrowError(
        errorBuilder.transaction.wrongImplement(tx.type, "TripService/postCashPaymentProcess"),
      );
    });
  });

  describe("postAdjustmentProcess", () => {
    it("should throw an error when the tx is not a tx_adjustment", async () => {
      const tx = new Tx();
      tx.type = TxTypes.TRIP;

      expect(service.postAdjustmentProcess(tx)).rejects.toThrowError(
        errorBuilder.transaction.wrongImplement(tx.type, "TripService/postAdjustmentProcess"),
      );
    });
  });

  describe("isTripCalculateCorrect", () => {
    let paymentTx1: PaymentTx;
    let paymentTx2: PaymentTx;
    let trip: TripDocument;
    let tx: Tx;

    beforeEach(() => {
      jest.clearAllMocks();
      paymentTx1 = generateFakePaymentTx();
      paymentTx2 = generateFakePaymentTx();
      trip = fakeTrip();
      trip.tripStatus = TripStatus.HIRED;
      trip.paymentType = PaymentType.DASH;

      tx = new Tx();
      tx.id = "1234";
      tx.type = TxTypes.TRIP;
      tx.metadata = trip;
      tx.paymentTx = [paymentTx1, paymentTx2];
    });

    it("should throw an error when the tx is not a txTrip", async () => {
      const tx = new Tx();
      tx.type = TxTypes.TX_ADJUSTMENT;

      expect(() => service.isTxCalculationCorrect(tx)).toThrowError(
        errorBuilder.transaction.wrongImplement(tx.type, "TripService/isTxCalculationCorrect"),
      );
    });

    it("should return false when trip's paymentType is not DASH", async () => {
      trip.paymentType = PaymentType.DASH_ADJUSTMENT;
      const result = service.isTxCalculationCorrect(tx);
      expect(result).toEqual({
        reason: "Not a DASH trip",
        result: false,
      });
    });

    it("should return false when paymentTx[] is empty", async () => {
      tx.paymentTx = [];
      const result = service.isTxCalculationCorrect(tx);
      expect(result).toEqual({ reason: "Trip has no tripTotal or total", result: false });
    });

    it("should return false when paymentTx are failed", async () => {
      paymentTx1.status = PaymentInformationStatus.FAILURE;
      paymentTx2.status = PaymentInformationStatus.FAILURE;
      const result = service.isTxCalculationCorrect(tx);
      expect(result).toEqual({ reason: "Trip has no tripTotal or total", result: false });
    });

    it("should return false when last paymentTx has no amount", async () => {
      paymentTx1.createdAt = new Date("2019-12-05T10:25:31.000Z");
      delete paymentTx2.amount;
      const result = service.isTxCalculationCorrect(tx);
      expect(result).toEqual({ reason: "Trip has no tripTotal or total", result: false });
    });

    it("should return false when trip doesn't have trip total", async () => {
      trip.total = 160;
      const result = service.isTxCalculationCorrect(tx);
      expect(result).toEqual({ reason: "Trip has no tripTotal or total", result: false });
    });

    it("should return false when auth amount is smaller than trip total", async () => {
      tx.total = 160;
      trip.tripTotal = 150;
      const result = service.isTxCalculationCorrect(tx);
      expect(result).toEqual({
        reason: "Auth amount is smaller than trip total",
        result: false,
      });
    });

    it("should return false when calculation is incorrect", async () => {
      tx.total = 100;
      trip.tripTotal = 90;
      trip.dashTips = 10;
      trip.dashFee = 1.5;
      tx.dashFee = 1.5;
      const result = service.isTxCalculationCorrect(tx);
      expect(result).toEqual({
        reason: "Total calculation is not correct. dashFee: 1.5, calculatedTotalForTaxiposOnly: 101.5, total: 100",
        result: false,
      });
    });

    it("should return true when calculation is correct", async () => {
      tx.total = 101.5;
      tx.dashFee = 1.5;
      trip.tripTotal = 90;
      trip.dashTips = 10;
      trip.dashFee = 1.5;
      const result = service.isTxCalculationCorrect(tx);
      expect(result).toEqual({
        reason: "OK!",
        result: true,
      });
    });
  });

  describe("sendReceipt", () => {
    let paymentTx: PaymentTx;
    let trip: TripDocument;
    let tx: Tx;

    beforeEach(() => {
      jest.clearAllMocks();
      paymentTx = generateFakePaymentTx();
      trip = fakeTrip();

      tx = new Tx();
      tx.id = "1234";
      tx.type = TxTypes.TRIP;
      tx.metadata = trip;
      paymentTx.tx = tx;

      mockPublishMessageForMessageProcessingParams.mockImplementation(() => {
        return () => new Promise((resolve) => resolve(""));
      });
    });

    it("should send event to PubSub to send receipt", async () => {
      await service.sendReceipt(tx, paymentTx);
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenCalledWith(expectedPublishWhatsappMessage);
    });

    it("should throw an error when the tx is not a txTrip", async () => {
      tx.type = TxTypes.TX_ADJUSTMENT;

      expect(service.sendReceipt(tx, new PaymentTx())).rejects.toThrow(
        errorBuilder.transaction.wrongImplement(tx.type, "TripService/sendReceipt"),
      );
    });

    it("should throw error when no gatewayTransactionId", async () => {
      delete paymentTx.gatewayTransactionId;

      expect(service.sendReceipt(tx, paymentTx)).rejects.toThrow(
        errorBuilder.validation.failed("'gatewayTransactionId' is required"),
      );
    });

    it("should throw error when no amount", async () => {
      delete paymentTx.amount;

      expect(service.sendReceipt(tx, paymentTx)).rejects.toThrow(
        errorBuilder.validation.failed("'amount' is required"),
      );
    });

    it("should throw error when no phone", async () => {
      delete trip.passengerInformation;

      expect(service.sendReceipt(tx, paymentTx)).rejects.toThrow(
        errorBuilder.validation.failed("'passengerInformation' is required"),
      );
    });

    it("should throw error when no language", async () => {
      delete trip.language;

      await service.sendReceipt(tx, paymentTx);
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenCalledWith(expectedPublishWhatsappMessage);
    });
  });

  describe("hasCustomerInformation", () => {
    it("should return false when there is no phone in merchant", () => {
      const tx = new Tx();
      tx.type = TxTypes.TRIP;
      tx.metadata = new TripDocument();
      tx.metadata.passengerInformation = null as unknown as PassengerInformation;
      const result = service.hasCustomerInformation(tx);
      expect(result).toEqual(false);
    });

    it("should throw an error when the tx is not a txTrip", () => {
      const tx = new Tx();
      tx.type = TxTypes.TX_ADJUSTMENT;

      expect(() => service.hasCustomerInformation(tx)).toThrowError(
        errorBuilder.transaction.wrongImplement(tx.type, "TripService/hasCustomerInformation"),
      );
    });
  });

  describe("isPayWithDash", () => {
    it("should return true when payment_type is DASH", () => {
      const tx = new Tx();
      tx.type = TxTypes.TRIP;
      tx.metadata = new TripDocument();
      tx.metadata.paymentType = PaymentType.DASH;
      tx.metadata.tripStart = new Date("2023-09-01T00:00:00.000Z");
      const result = service.isPayWithDash(tx);
      expect(result).toEqual(true);
    });

    it("should throw an error when the tx is not a txTrip", () => {
      const tx = new Tx();
      tx.type = TxTypes.TX_ADJUSTMENT;

      expect(() => service.isPayWithDash(tx)).toThrowError(
        errorBuilder.transaction.wrongImplement(tx.type, "TripService/isPayWithDash"),
      );
    });
  });

  describe("getExpiresAt", () => {
    it("should throw an error when the tx is not a txTrip", () => {
      const tx = new Tx();
      tx.type = TxTypes.TX_ADJUSTMENT;

      expect(service.getExpiresAt(tx)).rejects.toThrowError(
        errorBuilder.transaction.wrongImplement(tx.type, "TripService/getExpiresAt"),
      );
    });
  });

  describe("prePaymentProcess", () => {
    it("should throw an error when the tx is not a txTrip", async () => {
      const tx = new Tx();
      tx.type = TxTypes.TX_ADJUSTMENT;

      expect(service.prePaymentProcess(tx)).rejects.toThrowError(
        errorBuilder.transaction.wrongImplement(tx.type, "TripService/prePaymentProcess"),
      );
    });

    it("should throw an error when the trip has not ended", async () => {
      const tx = new Tx();
      tx.id = "1234";
      tx.type = TxTypes.TRIP;
      tx.metadata = new TripDocument();
      tx.metadata.tripEnd = null as unknown as Date;

      expect(service.prePaymentProcess(tx)).rejects.toThrowError(errorBuilder.transaction.trip.notEnded(tx.id));
    });
  });

  describe("getTxsToPayout", () => {
    let txs: Tx[] = [];
    let fileContent: PayoutBankFileRow[] = [];

    beforeEach(() => {
      const merchant1 = { phoneNumber: "+***********" } as Merchant;
      const merchant2 = { phoneNumber: "+***********" } as Merchant;
      const merchant3 = { phoneNumber: "+***********" } as Merchant;
      txs = [
        { id: "1", merchant: merchant1 } as Tx,
        { id: "2", merchant: merchant2 } as Tx,
        { id: "3", merchant: merchant1 } as Tx,
        { id: "4", merchant: merchant3 } as Tx,
      ];

      fileContent = [
        {
          merchantId: "+***********",
          amount: 10,
          status: BankResponseLineItemStatus.COMPLETED,
          originalBatchFileName: "payout-149a8a84-6da2-4bbe-a315-46db783f2e7e.txt",
        } as PayoutBankFileRow,
        {
          merchantId: "+***********",
          amount: 20,
          status: BankResponseLineItemStatus.CONFIRMED,
          originalBatchFileName: "payout-149a8a84-6da2-4bbe-a315-46db783f2e7e.txt",
        } as PayoutBankFileRow,
        {
          merchantId: "+***********",
          amount: 30,
          status: BankResponseLineItemStatus.REJECTED,
          originalBatchFileName: "payout-149a8a84-6da2-4bbe-a315-46db783f2e7e.txt",
        } as PayoutBankFileRow,
      ];
    });

    it("Should return the completed and failed txs", () => {
      const payouts = [
        {
          id: "149a8a84-6da2-4bbe-a315-46db783f2e7e",
          originalRequest: { bankName: "DBS", txIds: ["1", "2", "3", "4"] },
          batchFile: "payout-149a8a84-6da2-4bbe-a315-46db783f2e7e.txt",
        } as Payout,
      ];
      expect(service.getTxsToPayout(txs, fileContent, payouts)).toEqual({
        completed: expect.arrayContaining([txs[0], txs[1], txs[2]]),
        failed: expect.arrayContaining([txs[3]]),
        bankProcessing: expect.arrayContaining([]),
      });
    });
  });

  describe("postPayoutProcess", () => {
    let txs: Tx[] = [];
    const notification: NotificationDocument = {
      created_on: expect.any(Date),
      locale: LanguageOption.ZHHK,
      message: notificationTemplate[NotificationType.PAYOUT_PAID].body,
      status: NotificationStatus.NEW,
      title: notificationTemplate[NotificationType.PAYOUT_PAID].title,
      type: NotificationDocumentType.PAYOUT,
    };

    beforeEach(() => {
      const merchant1 = { phoneNumber: "+***********" } as Merchant;
      const merchant2 = { phoneNumber: "+***********" } as Merchant;
      const merchant3 = { phoneNumber: "+***********" } as Merchant;
      txs = [
        { id: "1", merchant: merchant1 } as Tx,
        { id: "2", merchant: merchant2 } as Tx,
        { id: "3", merchant: merchant1 } as Tx,
        { id: "4", merchant: merchant3 } as Tx,
      ];
    });

    it("Should return the completed and failed txs", async () => {
      const result = await service.postPayoutProcess(txs);

      expect(result).toEqual(txs);

      expect(addMock).toHaveBeenCalledTimes(3);

      expect(addMock).toHaveBeenNthCalledWith(1, notification);
      expect(addMock).toHaveBeenNthCalledWith(2, notification);
      expect(addMock).toHaveBeenNthCalledWith(3, notification);
    });
  });

  describe("getReceiptByTx", () => {
    let tx: Tx;
    let expectedTxReceiptTrip: TxReceiptTrip;
    beforeEach(() => {
      jest.clearAllMocks();
      tx = new Tx();
      tx.id = "1234";
      tx.type = TxTypes.TRIP;
      tx.metadata = {
        ...fakeTripData,
        licensePlate: "TEST1234",
        tripEnd: new Date("2023-01-01T00:01:00.000Z"),
        locationEnd: { _latitude: 22.46569995, _longitude: 114.00413758 },
        locationEndAddress: "天水圍慧景軒3座",
        tripStart: new Date("2023-01-01T00:00:00.000Z"),
        locationStart: { _latitude: 22.46571212, _longitude: 114.00406294 },
        locationStartAddress: "天水圍慧景軒3座",
        paymentType: "DASH",
        tripTotal: 22,
        isDashMeter: true,
      } as unknown as TripDocument;
      // Default expected result, VISA card
      expectedTxReceiptTrip = new TxReceiptTrip();
      expectedTxReceiptTrip.adjustment = 0;
      expectedTxReceiptTrip.dashFee = 0;
      expectedTxReceiptTrip.dashTips = 0;
      expectedTxReceiptTrip.distance = 0;
      expectedTxReceiptTrip.extra = 0;
      expectedTxReceiptTrip.fare = 22;
      expectedTxReceiptTrip.additionalBookingFee = 0;
      expectedTxReceiptTrip.boostAmount = 0;
      expectedTxReceiptTrip.id = "1234";
      expectedTxReceiptTrip.language = "en";
      expectedTxReceiptTrip.licensePlate = "TEST1234";
      expectedTxReceiptTrip.locationEnd = { _latitude: 22.46569995, _longitude: 114.00413758 };
      expectedTxReceiptTrip.locationEndAddress = "天水圍慧景軒3座";
      expectedTxReceiptTrip.locationStart = { _latitude: 22.46571212, _longitude: 114.00406294 };
      expectedTxReceiptTrip.locationStartAddress = "天水圍慧景軒3座";
      expectedTxReceiptTrip.paymentType = PaymentType.DASH;
      expectedTxReceiptTrip.total = 0;
      expectedTxReceiptTrip.tripTotal = 22;
      expectedTxReceiptTrip.tripEnd = new Date("2023-01-01T00:01:00.000Z");
      expectedTxReceiptTrip.tripStart = new Date("2023-01-01T00:00:00.000Z");
      const expectedPaymentCardInformation = new PaymentCardInformation();
      expectedPaymentCardInformation.cardType = "VISA";
      expectedPaymentCardInformation.maskedPan = "8464*****212341";
      expectedTxReceiptTrip.paymentCardInformation = expectedPaymentCardInformation;
      expectedTxReceiptTrip.isDashMeter = true;
      expectedTxReceiptTrip.isHailing = false;
      expectedTxReceiptTrip.billing = { total: 23.5 } as any as Billing;
    });

    it("should return a TxReceipt when tx is found and payment tx is auth and capture", async () => {
      tx.paymentTx = generateFakePaymentTxAuthAndCapture();
      const result = await service.getReceiptByTx(tx, ReceiptLanguageType.ENHK);
      expect(result).toEqual(expectedTxReceiptTrip);
    });

    it("should return a TxReceipt when tx is found and payment tx is sale", async () => {
      tx.paymentTx = generateFakePaymentTxSale();
      const result = await service.getReceiptByTx(tx, ReceiptLanguageType.ENHK);
      expect(result).toEqual(expectedTxReceiptTrip);
    });

    it("should return a TxReceipt when tx is found and payment tx is Alipay sale", async () => {
      tx.paymentTx = generateFakePaymentTxAlipaySale();
      const result = await service.getReceiptByTx(tx, ReceiptLanguageType.ENHK);
      expectedTxReceiptTrip.paymentCardInformation.cardType = "ALIPAY";
      expect(result).toEqual(expectedTxReceiptTrip);
    });

    it("should throw an error when the tx is not a txTrip", async () => {
      const tx = new Tx();
      tx.type = TxTypes.TX_ADJUSTMENT;

      await expect(service.getReceiptByTx(tx, ReceiptLanguageType.ENHK)).rejects.toThrow(
        errorBuilder.transaction.wrongImplement(tx.type, "TripService/getReceiptByTx"),
      );
    });
  });

  describe("updateMetadata", () => {
    it("should throw an error when the tx is not a txTrip", async () => {
      const tx = new Tx();
      tx.type = TxTypes.TX_ADJUSTMENT;

      expect(service.updateMetadata(tx, {})).rejects.toThrowError(
        errorBuilder.transaction.wrongImplement(tx.type, "TripService/updateMetadata"),
      );
    });

    it("should update everywhere", async () => {
      const tx = new Tx();
      tx.id = "1234";
      tx.type = TxTypes.TRIP;
      tx.merchant = { phoneNumber: "+***********" } as Merchant;
      const trip = fakeTrip();
      trip.session = { id: "12345678" };
      tx.metadata = trip;

      updateMeterTripMock.mockReturnValueOnce(new Promise((resolve) => resolve(new TripDocument())));
      findOneByIdMock.mockReturnValueOnce(new Promise((resolve) => resolve(new DriverTripDocument())));
      await service.updateMetadata(tx, { paymentType: "" });
      expect(updateMeterTripMock).toBeCalledWith("1234", { paymentType: "" });
      expect(updateDriverTripWithObjectMock).toBeCalledWith("1234", { paymentType: "" });
      expect(updateSessionTripWithObjectMock).toBeCalledWith("1234", { paymentType: "" });
      expect(mockSave).toBeCalled();
    });
  });

  describe("unlock", () => {
    beforeEach(() => {
      jest.clearAllMocks();

      getActiveLockMock.mockImplementation(() => {
        return new Promise((resolve) =>
          resolve({ id: "1234", unlockedAt: null, createdAt: new Date(), expiredAt: new Date(), createdBy: "user123" }),
        );
      });
    });

    it("should call the unlock", async () => {
      const tx = { id: "1234", type: TxTypes.TRIP, metadata: { licensePlate: "PLATE_1" } } as Tx;
      const result = await service.unlock(tx, "user123");
      expect(result).toEqual(tx);

      expect(getActiveLockMock).toHaveBeenCalledTimes(1);
      expect(setMock).toHaveBeenCalledWith({
        id: "1234",
        unlockedAt: expect.any(Date),
        createdAt: expect.any(Date),
        expiredAt: expect.any(Date),
        createdBy: "user123",
      });
    });

    it("should throw an error when there is no active lock", async () => {
      getActiveLockMock.mockImplementation(() => {
        return new Promise((resolve) => resolve(undefined));
      });

      await expect(
        service.unlock({ id: "1234", type: TxTypes.TRIP, metadata: { licensePlate: "PLATE_1" } } as Tx, "user123"),
      ).rejects.toThrow(errorBuilder.transaction.alreadyUnlocked("1234"));

      expect(getActiveLockMock).toHaveBeenCalledTimes(1);
      expect(setMock).not.toHaveBeenCalled();
    });

    it("should throw an error when there is no license plate in the metadata", async () => {
      await expect(service.unlock({ id: "1234", type: TxTypes.TRIP, metadata: {} } as Tx, "user123")).rejects.toThrow(
        errorBuilder.transaction.trip.missingLicencePlate("1234"),
      );

      expect(getActiveLockMock).not.toHaveBeenCalled();
      expect(setMock).not.toHaveBeenCalled();
    });

    it("should throw an error when the tx type is not trip", async () => {
      await expect(
        service.unlock(
          { id: "1234", type: TxTypes.TX_ADJUSTMENT, metadata: { licensePlate: "PLATE_1" } } as Tx,
          "user123",
        ),
      ).rejects.toThrow(errorBuilder.transaction.wrongImplement(TxTypes.TX_ADJUSTMENT, "TripService/unlock"));

      expect(getActiveLockMock).not.toHaveBeenCalled();
      expect(setMock).not.toHaveBeenCalled();
    });

    it("should throw an error when the tx is locked by another user", async () => {
      getActiveLockMock.mockImplementation(() => {
        return new Promise((resolve) =>
          resolve({
            id: "1234",
            unlockedAt: new Date(),
            createdAt: new Date(),
            expiredAt: new Date(),
            createdBy: "user123",
          }),
        );
      });

      await expect(
        service.unlock({ id: "1234", type: TxTypes.TRIP, metadata: { licensePlate: "PLATE_1" } } as Tx, "user456"),
      ).rejects.toThrow(errorBuilder.transaction.lockedByOtherUser("1234"));

      expect(getActiveLockMock).toHaveBeenCalledTimes(1);
      expect(setMock).not.toHaveBeenCalled();
    });
  });

  describe("setTipFromUserApp", () => {
    it("should throw an error when the tx type is not trip", async () => {
      await expect(
        service.setTipFromUserApp(
          { id: "1234", type: TxTypes.TX_ADJUSTMENT, metadata: { licensePlate: "PLATE_1" } } as Tx,
          "user123",
          15,
        ),
      ).rejects.toThrow(
        errorBuilder.transaction.wrongImplement(TxTypes.TX_ADJUSTMENT, "TripService/setTipFromUserApp"),
      );

      expect(setMock).not.toHaveBeenCalled();
    });
    it("should throw an error when the trip ended", async () => {
      await expect(
        service.setTipFromUserApp(
          {
            id: "1234",
            type: TxTypes.TRIP,
            metadata: { licensePlate: "PLATE_1", tripEnd: new Date("2023-05-22T22:49:12.000Z") },
          } as Tx,
          "user123",
          15,
        ),
      ).rejects.toThrow(errorBuilder.transaction.trip.tripAlreadyEnded("1234"));

      expect(setMock).not.toHaveBeenCalled();
    });
    it("should throw an error when the trip not paired", async () => {
      await expect(
        service.setTipFromUserApp(
          {
            id: "1234",
            type: TxTypes.TRIP,
            metadata: { licensePlate: "PLATE_1" },
          } as Tx,
          "user123",
          15,
        ),
      ).rejects.toThrow(errorBuilder.transaction.trip.tripNotPairedWithUser("1234", "user123"));

      expect(setMock).not.toHaveBeenCalled();
    });
  });

  describe("setRating", () => {
    it("should throw an error when the tx type is not trip", async () => {
      await expect(
        service.setRating(
          { id: "1234", type: TxTypes.TX_ADJUSTMENT, metadata: { licensePlate: "PLATE_1" } } as Tx,
          "user123",
          { isUserEnjoy: true },
        ),
      ).rejects.toThrow(errorBuilder.transaction.wrongImplement(TxTypes.TX_ADJUSTMENT, "TripService/setRating"));

      expect(setMock).not.toHaveBeenCalled();
    });
    it("should throw an error when the trip not paired", async () => {
      await expect(
        service.setRating(
          {
            id: "1234",
            type: TxTypes.TRIP,
            metadata: { licensePlate: "PLATE_1" },
          } as Tx,
          "user123",
          { isUserEnjoy: true },
        ),
      ).rejects.toThrow(errorBuilder.transaction.trip.tripNotPairedWithUser("1234", "user123"));

      expect(setMock).not.toHaveBeenCalled();
    });
  });

  describe("shouldAddProcessingFlag", () => {
    it("should return true when there is a processing status in payment_information", () => {
      const result = service.shouldAddProcessingFlag({
        ...fakeTripData,
        payment_information: [
          {
            creation_time: new Timestamp(1, 1),
            type: "SALE",
            body: JSON.stringify(soepayAuthSuccess),
            status: PaymentInformationStatus.SUCCESS,
          },
          {
            creation_time: new Timestamp(1, 1),
            type: "SALE",
            body: JSON.stringify(soepayAuthSuccess),
            status: PaymentInformationStatus.PROCESSING,
          },
        ],
      });
      expect(result).toEqual(true);
    });
    it("should return true when first status is success in payment_information", () => {
      const result = service.shouldAddProcessingFlag({
        ...fakeTripData,
        payment_information: [
          {
            creation_time: new Timestamp(1, 1),
            type: "SALE",
            body: JSON.stringify(soepayAuthSuccess),
            status: PaymentInformationStatus.SUCCESS,
          },
          {
            creation_time: new Timestamp(1, 1),
            type: "SALE",
            body: JSON.stringify(soepayAuthSuccess),
            status: PaymentInformationStatus.FAILURE,
          },
        ],
      });
      expect(result).toEqual(true);
    });
    it("should return false when last status is success in payment_information", () => {
      const result = service.shouldAddProcessingFlag({
        ...fakeTripData,
        payment_information: [
          {
            creation_time: new Timestamp(1, 1),
            type: "SALE",
            body: JSON.stringify(soepayAuthSuccess),
            status: PaymentInformationStatus.FAILURE,
          },
          {
            creation_time: new Timestamp(1, 1),
            type: "SALE",
            body: JSON.stringify(soepayAuthSuccess),
            status: PaymentInformationStatus.SUCCESS,
          },
        ],
      });
      expect(result).toEqual(true);
    });
    it("should return false when all are fail in payment_information", () => {
      const result = service.shouldAddProcessingFlag({
        ...fakeTripData,
        payment_information: [
          {
            creation_time: new Timestamp(1, 1),
            type: "SALE",
            body: JSON.stringify(soepayAuthSuccess),
            status: PaymentInformationStatus.FAILURE,
          },
          {
            creation_time: new Timestamp(1, 1),
            type: "SALE",
            body: JSON.stringify(soepayAuthSuccess),
            status: PaymentInformationStatus.FAILURE,
          },
        ],
      });
      expect(result).toEqual(false);
    });
    it("should return true when there is just one success in payment_information", () => {
      const result = service.shouldAddProcessingFlag({
        ...fakeTripData,
        payment_information: [
          {
            creation_time: new Timestamp(1, 1),
            type: "SALE",
            body: JSON.stringify(soepayAuthSuccess),
            status: PaymentInformationStatus.SUCCESS,
          },
        ],
      });
      expect(result).toEqual(true);
    });
  });
});
