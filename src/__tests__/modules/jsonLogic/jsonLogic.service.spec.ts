import { RuleParams } from "@nest/modules/jsonLogic/dto/ruleParams.dto";
import { JsonLogicService } from "@nest/modules/jsonLogic/jsonLogic.service";
import { SubTxTypes, TxTypes } from "@nest/modules/transaction/dto/txType.dto";
import { newTestLocationService } from "@tests/utils/services/TestServices.specs.utils";

describe("JsonLogicService", () => {
  let jsonLogicService: JsonLogicService;
  beforeEach(() => {
    jsonLogicService = new JsonLogicService(newTestLocationService());
  });

  describe("apply", () => {
    it("should return true for matching transactionType", () => {
      const logic = {
        if: [{ "==": [{ var: "transactionType" }, TxTypes.TRIP] }, true, false],
      };
      const data = new RuleParams({
        transactionType: TxTypes.TRIP,
      });
      const result = jsonLogicService.apply(logic, data);
      expect(result).toBe(true);
    });

    it("should return false for non-matching transactionType", () => {
      const logic = {
        if: [{ "==": [{ var: "transactionType" }, TxTypes.TRIP] }, true, false],
      };
      const data = new RuleParams({
        transactionType: TxTypes.HAILING_REQUEST,
      });
      const result = jsonLogicService.apply(logic, data);
      expect(result).toBe(false);
    });

    it("should return number for matching transactionType with number output", () => {
      const logic = {
        if: [{ "==": [{ var: "transactionType" }, TxTypes.TRIP] }, 5, 0],
      };
      const data = new RuleParams({
        transactionType: TxTypes.TRIP,
      });
      const result = jsonLogicService.apply(logic, data);
      expect(result).toBe(5);
    });

    it("should return 0 for non-matching transactionType with number output", () => {
      const logic = {
        if: [{ "==": [{ var: "transactionType" }, TxTypes.TRIP] }, 5, 0],
      };
      const data = new RuleParams({
        transactionType: TxTypes.HAILING_REQUEST,
      });
      const result = jsonLogicService.apply(logic, data);
      expect(result).toBe(0);
    });

    it("should return true for multiple conditions with AND logic", () => {
      const logic = {
        and: [
          { "==": [{ var: "transactionType" }, TxTypes.TRIP] },
          { "==": [{ var: "transactionSubtype" }, SubTxTypes.HAILING] },
        ],
      };
      const data = new RuleParams({
        transactionType: TxTypes.TRIP,
        transactionSubtype: SubTxTypes.HAILING,
      });
      const result = jsonLogicService.apply(logic, data);
      expect(result).toBe(true);
    });

    it("should return false for multiple conditions with AND logic when one condition fails", () => {
      const logic = {
        and: [
          { "==": [{ var: "transactionType" }, TxTypes.TRIP] },
          { "==": [{ var: "transactionSubtype" }, SubTxTypes.HAILING] },
        ],
      };
      const data = new RuleParams({
        transactionType: TxTypes.TRIP,
        transactionSubtype: SubTxTypes.STREET,
      });
      const result = jsonLogicService.apply(logic, data);
      expect(result).toBe(false);
    });

    it("should return true for point-in-polygon logic", () => {
      const logic = {
        point_in_polygon: [
          { var: "origin" },
          [
            [114.0, 22.0],
            [114.1, 22.1],
            [114.2, 22.2],
          ],
        ],
      };
      const data = new RuleParams({
        origin: [114.05, 22.05],
      });
      const result = jsonLogicService.apply(logic, data);
      expect(result).toBe(true);
    });

    it("should return false for point-in-polygon logic", () => {
      const logic = {
        point_in_polygon: [
          { var: "origin" },
          [
            [114.0, 22.0],
            [114.1, 22.1],
            [114.2, 22.2],
          ],
        ],
      };
      const data = new RuleParams({
        origin: [113.5, 22.05],
      });
      const result = jsonLogicService.apply(logic, data);
      expect(result).toBe(false);
    });

    it("should return true for time-based between logic", () => {
      const logic = {
        time_between: [{ var: "timeOfDay" }, "17:00", "19:00"],
      };
      const data = new RuleParams({
        timeOfDay: "18:30",
      });
      const result = jsonLogicService.apply(logic, data);
      expect(result).toBe(true);
    });

    it("should return false for time-based between logic", () => {
      const logic = {
        time_between: [{ var: "timeOfDay" }, "17:00", "19:00"],
      };
      const data = new RuleParams({
        timeOfDay: "13:30",
      });
      const result = jsonLogicService.apply(logic, data);
      expect(result).toBe(false);
    });

    it("should return true for cross-day time-based between logic", () => {
      const logic = {
        time_between: [{ var: "timeOfDay" }, "22:00", "02:00"],
      };
      const data = new RuleParams({
        timeOfDay: "23:30",
      });
      const result = jsonLogicService.apply(logic, data);
      expect(result).toBe(true);
    });

    it("should return false for polygon when no coordinates are provided", () => {
      const logic = {
        point_in_polygon: [
          { var: "origin" },
          [
            [114.0, 22.0],
            [114.1, 22.1],
            [114.2, 22.2],
          ],
        ],
      };
      const data = new RuleParams({});
      const result = jsonLogicService.apply(logic, data);
      expect(result).toBe(false);
    });

    it("should return false for time when no time is provided", () => {
      const logic = {
        time_between: [{ var: "timeOfDay" }, "17:00", "19:00"],
      };
      const data = new RuleParams({});
      const result = jsonLogicService.apply(logic, data);
      expect(result).toBe(false);
    });
  });
});
