export const mockPublishMessageForTripProcessing = jest.fn();
export const mockPublishMessageForMessageProcessingParams = jest.fn();
export const mockPublishMessageForCaptureProcessing = jest.fn();
export const mockPublishMessageForVoidProcessing = jest.fn();
export const mockPublishMessageForCopyTripToDriverProcessing = jest.fn();
export const mockPublishMessageForCampaignTriggerProcessing = jest.fn();
export const mockPublishMessageForHailingStatusChanged = jest.fn();
export const mockPublishMessageForHailingTxCreated = jest.fn();
class FakePubSubService {
  publishMessageForTripProcessing = mockPublishMessageForTripProcessing;
  publishMessageForMessageProcessingParams = mockPublishMessageForMessageProcessingParams;
  publishMessageForCaptureProcessing = mockPublishMessageForCaptureProcessing;
  publishMessageForVoidProcessing = mockPublishMessageForVoidProcessing;
  publishMessageForCopyTripToDriverProcessing = mockPublishMessageForCopyTripToDriverProcessing;
  publishMessageForCampaignTriggerProcessing = mockPublishMessageForCampaignTriggerProcessing;
  publishMessageForHailingStatusChanged = mockPublishMessageForHailingStatusChanged;
  publishMessageForHailingTxCreated = mockPublishMessageForHailingTxCreated;
}

export default FakePubSubService;
