# Dash - Server

## Project init

**Note: For dev, make sure to use the project id for Project `dash-dev2`**

### Firebase

```bash
firebase projects:list
firebase use <project id>
```

#### Adding new firebase function

1. Add the function in `src/functions/nest.function.ts`
2. Add the function in `src/index.ts`
3. run command to update sql string `./scripts/updateConnectionStringCloudRun.sh <project id> <cloudsql instance id>`

### Google PubSub

```bash
gcloud services enable pubsub.googleapis.com
gcloud projects list
gcloud config set project <project id>
```

_Create Topic_

```bash
curl -X PUT http://localhost:8085/v1/projects/dash-dev2-edcb3/topics/batch-job
```

_Get messages_
```bash
curl -H 'content-type: application/json' -X POST -d '{"returnImmediately":"false", "maxMessages":"1"}' http://localhost:8085/v1/projects/dash-dev2-edcb3/topics/batch-job:pull
```

_List all topics_
```bash
curl http://localhost:8085/v1/projects/dash-dev2-edcb3/topics
```

## Dev

1. Start the postgres dependency: `docker compose up -d`
1. Install firebase cli: `npm i -g firebase-tools`
1. `tsc -w` or `yarn build --watch`
1. `firebase emulators:start --export-on-exit=./.data --import=./.data`
1. If emulators cannot start due to used ports, just update [the emulator config](firebase.json)
1. To test, simply use postman, or the [swagger docs](http://127.0.0.1:5001/dash-dev2-edcb3/asia-east2/api/api#/)

**Follow the [docs here](https://www.notion.so/d-ash/Environment-ff4318106b1a494b98d16ec037640b0c?source=copy_link) to create admin user and obtain JWT**

## Deployment

```bash
firebase projects:list
firebase use <project>
firebase deploy --only functions:<function name>
firebase deploy
```

## To-do

1. Swtich to use yum and add input validation
1. move trigger to another module
1. clean up loggings

## Debug

### check log

```bash
firebase functions:log --only app
```

## env switch
```bash
firebase use dash-dev-81bb1
firebase use dash-dev2-edcb3
firebase use prod-...
```
or shorthand 
```bash
firebase use dev
firebase use qa
```